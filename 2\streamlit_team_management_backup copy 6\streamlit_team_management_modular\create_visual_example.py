#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建换装逻辑的可视化示例
Create Visual Example of Fashion Try-On Logic
"""

import os
from PIL import Image, ImageDraw, ImageFont
import matplotlib.pyplot as plt
import matplotlib.patches as patches

def create_visual_example():
    """创建换装逻辑的可视化示例"""
    print("🎨 创建换装逻辑可视化示例...")
    
    os.makedirs("visual_example", exist_ok=True)
    
    # 创建示例图片
    create_model_example()
    create_clothing_example()
    create_process_diagram()
    create_result_comparison()
    
    print("✅ 可视化示例创建完成！")
    print("📁 查看 visual_example/ 文件夹中的图片")

def create_model_example():
    """创建模特照片示例"""
    width, height = 300, 400
    
    # 创建好的模特照片示例
    good_img = Image.new('RGB', (width, height), 'lightblue')
    draw = ImageDraw.Draw(good_img)
    
    # 绘制人物轮廓
    # 头部
    draw.ellipse([120, 50, 180, 110], fill='peachpuff', outline='black', width=2)
    
    # 身体
    draw.rectangle([110, 110, 190, 250], fill='white', outline='black', width=2)
    
    # 手臂
    draw.rectangle([90, 120, 110, 200], fill='peachpuff', outline='black', width=2)
    draw.rectangle([190, 120, 210, 200], fill='peachpuff', outline='black', width=2)
    
    # 腿部
    draw.rectangle([120, 250, 150, 350], fill='blue', outline='black', width=2)
    draw.rectangle([150, 250, 180, 350], fill='blue', outline='black', width=2)
    
    # 添加标签
    try:
        font = ImageFont.truetype("arial.ttf", 16)
    except:
        font = ImageFont.load_default()
    
    draw.text((10, 10), "✅ 好的模特照片", fill='green', font=font)
    draw.text((10, 30), "• 正面站立", fill='black')
    draw.text((10, 50), "• 人物清晰", fill='black')
    draw.text((10, 70), "• 服装可见", fill='black')
    
    good_img.save("visual_example/good_model_photo.png")
    
    # 创建不好的模特照片示例
    bad_img = Image.new('RGB', (width, height), 'lightcoral')
    draw = ImageDraw.Draw(bad_img)
    
    # 绘制侧身人物
    draw.ellipse([200, 50, 250, 100], fill='peachpuff', outline='black', width=2)
    draw.polygon([(180, 100), (220, 100), (210, 200), (170, 200)], fill='white', outline='black', width=2)
    
    draw.text((10, 10), "❌ 不好的模特照片", fill='red', font=font)
    draw.text((10, 30), "• 侧身角度", fill='black')
    draw.text((10, 50), "• 服装遮挡", fill='black')
    draw.text((10, 70), "• 不够清晰", fill='black')
    
    bad_img.save("visual_example/bad_model_photo.png")

def create_clothing_example():
    """创建服装图片示例"""
    width, height = 300, 400
    
    # 创建好的服装图片示例
    good_img = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(good_img)
    
    # 绘制T恤
    # T恤主体
    shirt_points = [
        (100, 100), (200, 100), (220, 120), (220, 300),
        (80, 300), (80, 120)
    ]
    draw.polygon(shirt_points, fill='royalblue', outline='navy', width=3)
    
    # 袖子
    draw.polygon([(80, 120), (60, 130), (60, 180), (80, 170)], fill='royalblue', outline='navy', width=2)
    draw.polygon([(220, 120), (240, 130), (240, 180), (220, 170)], fill='royalblue', outline='navy', width=2)
    
    # 领口
    draw.polygon([(130, 100), (170, 100), (160, 130), (140, 130)], fill='white', outline='navy', width=2)
    
    try:
        font = ImageFont.truetype("arial.ttf", 16)
    except:
        font = ImageFont.load_default()
    
    draw.text((10, 10), "✅ 好的服装图片", fill='green', font=font)
    draw.text((10, 350), "• 服装完整", fill='black')
    draw.text((10, 370), "• 背景简单", fill='black')
    draw.text((10, 390), "• 清晰度高", fill='black')
    
    good_img.save("visual_example/good_clothing_photo.png")
    
    # 创建不好的服装图片示例
    bad_img = Image.new('RGB', (width, height), 'lightgray')
    draw = ImageDraw.Draw(bad_img)
    
    # 绘制皱巴巴的衣服
    wrinkled_points = [
        (100, 150), (180, 140), (200, 160), (190, 250),
        (120, 280), (90, 200)
    ]
    draw.polygon(wrinkled_points, fill='darkred', outline='black', width=2)
    
    # 添加复杂背景
    for i in range(0, width, 20):
        for j in range(0, height, 20):
            if (i + j) % 40 == 0:
                draw.rectangle([i, j, i+10, j+10], fill='gray')
    
    draw.text((10, 10), "❌ 不好的服装图片", fill='red', font=font)
    draw.text((10, 350), "• 服装皱巴", fill='black')
    draw.text((10, 370), "• 背景复杂", fill='black')
    draw.text((10, 390), "• 不够清晰", fill='black')
    
    bad_img.save("visual_example/bad_clothing_photo.png")

def create_process_diagram():
    """创建处理流程图"""
    fig, ax = plt.subplots(1, 1, figsize=(14, 8))
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 步骤框
    steps = [
        {"name": "模特照片\n(人物)", "x": 1, "y": 4, "color": "lightblue"},
        {"name": "服装图片\n(衣服)", "x": 1, "y": 2, "color": "lightgreen"},
        {"name": "步骤1\nAI换装", "x": 4, "y": 3, "color": "orange"},
        {"name": "步骤2\n背景移除", "x": 7, "y": 3, "color": "yellow"},
        {"name": "步骤3\n白底合成", "x": 10, "y": 3, "color": "pink"},
        {"name": "最终结果\n(换装完成)", "x": 13, "y": 3, "color": "lightcoral"}
    ]
    
    # 绘制步骤框
    for step in steps:
        rect = patches.Rectangle((step["x"]-0.8, step["y"]-0.5), 1.6, 1, 
                               linewidth=2, edgecolor='black', facecolor=step["color"])
        ax.add_patch(rect)
        ax.text(step["x"], step["y"], step["name"], ha='center', va='center', 
                fontsize=10, weight='bold')
    
    # 绘制箭头
    arrows = [
        (1.8, 4, 2.2, 3.5),    # 模特照片 -> AI换装
        (1.8, 2, 2.2, 2.5),    # 服装图片 -> AI换装
        (4.8, 3, 6.2, 3),      # AI换装 -> 背景移除
        (7.8, 3, 9.2, 3),      # 背景移除 -> 白底合成
        (10.8, 3, 12.2, 3)     # 白底合成 -> 最终结果
    ]
    
    for arrow in arrows:
        ax.annotate('', xy=(arrow[2], arrow[3]), xytext=(arrow[0], arrow[1]),
                   arrowprops=dict(arrowstyle='->', lw=2, color='red'))
    
    # 添加成本和时间信息
    costs = [
        {"text": "0.1 PTC\n2-3分钟", "x": 4, "y": 1.5},
        {"text": "0.5 PTC\n30秒-1分钟", "x": 7, "y": 1.5},
        {"text": "免费\n几秒钟", "x": 10, "y": 1.5}
    ]
    
    for cost in costs:
        ax.text(cost["x"], cost["y"], cost["text"], ha='center', va='center',
                fontsize=9, style='italic', bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow"))
    
    ax.set_xlim(0, 14)
    ax.set_ylim(0, 5)
    ax.set_title('🎯 换装处理流程图', fontsize=16, weight='bold', pad=20)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig("visual_example/process_diagram.png", dpi=300, bbox_inches='tight')
    plt.close()

def create_result_comparison():
    """创建结果对比图"""
    fig, axes = plt.subplots(1, 4, figsize=(16, 6))
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    
    # 模拟四个阶段的图片
    stages = [
        {"title": "原始照片", "color": "lightblue", "desc": "球员穿白色队服"},
        {"title": "换装结果", "color": "lightgreen", "desc": "球员穿红色队服"},
        {"title": "去除背景", "color": "white", "desc": "透明背景"},
        {"title": "白底合成", "color": "white", "desc": "白底最终结果"}
    ]
    
    for i, (ax, stage) in enumerate(zip(axes, stages)):
        # 创建示例图像
        ax.add_patch(patches.Rectangle((0.2, 0.3), 0.6, 0.4, 
                                     facecolor=stage["color"], edgecolor='black', linewidth=2))
        
        # 添加人物轮廓
        if i < 3:  # 前三个阶段显示背景
            ax.add_patch(patches.Rectangle((0, 0), 1, 1, 
                                         facecolor='lightgray', alpha=0.3))
        
        # 人物
        ax.add_patch(patches.Circle((0.5, 0.6), 0.1, facecolor='peachpuff', edgecolor='black'))
        ax.add_patch(patches.Rectangle((0.4, 0.4), 0.2, 0.3, 
                                     facecolor='red' if i >= 1 else 'white', edgecolor='black'))
        
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.set_title(stage["title"], fontsize=12, weight='bold')
        ax.text(0.5, 0.1, stage["desc"], ha='center', va='center', fontsize=9)
        ax.axis('off')
    
    plt.suptitle('🔄 换装结果对比', fontsize=16, weight='bold')
    plt.tight_layout()
    plt.savefig("visual_example/result_comparison.png", dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """主函数"""
    print("🎨 创建换装逻辑可视化示例")
    print("=" * 50)
    
    create_visual_example()
    
    print("\n📋 生成的文件：")
    print("  📸 good_model_photo.png - 好的模特照片示例")
    print("  📸 bad_model_photo.png - 不好的模特照片示例")
    print("  👕 good_clothing_photo.png - 好的服装图片示例")
    print("  👕 bad_clothing_photo.png - 不好的服装图片示例")
    print("  🔄 process_diagram.png - 处理流程图")
    print("  📊 result_comparison.png - 结果对比图")
    
    print("\n🎯 现在您可以查看这些图片来理解换装逻辑！")

if __name__ == "__main__":
    main()
