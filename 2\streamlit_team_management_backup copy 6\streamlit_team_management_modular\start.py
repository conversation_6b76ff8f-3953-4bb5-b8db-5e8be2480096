#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多页面应用启动器
Multi-page Application Launcher

支持球队管理系统和AI图片编辑器的双入口设计
"""

import streamlit as st
import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)


def configure_multipage():
    """配置多页面应用"""
    st.set_page_config(
        page_title="球队管理系统",
        page_icon="⚽",
        layout="wide",
        initial_sidebar_state="expanded"
    )


def render_welcome_page():
    """渲染欢迎页面"""
    st.title("🏆 欢迎使用球队管理系统")
    
    st.markdown("""
    <div style="text-align: center; padding: 40px;">
        <h2>选择您需要的功能</h2>
        <p style="font-size: 18px; color: #666; margin-bottom: 40px;">
            我们提供两个独立的应用程序，满足不同的使用需求
        </p>
    </div>
    """, unsafe_allow_html=True)
    
    # 功能选择区域
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                    padding: 30px; border-radius: 15px; color: white; text-align: center; margin: 20px;">
            <h3>⚽ 球队管理系统</h3>
            <p>完整的球队管理解决方案</p>
        </div>
        """, unsafe_allow_html=True)
        
        st.markdown("### 🎯 主要功能")
        st.markdown("""
        - 🏆 **球队管理**：创建和管理多个球队
        - 👥 **球员管理**：添加、编辑球员信息
        - 📸 **照片管理**：批量上传和管理球员照片
        - 🤖 **AI助手**：智能收集球队报名信息
        - 🎨 **批量修图**：为球队球员进行批量AI照片处理
        - 📋 **报名表生成**：自动生成比赛报名表
        """)
        
        if st.button("🚀 进入球队管理系统", type="primary", use_container_width=True):
            st.switch_page("app.py")
    
    with col2:
        st.markdown("""
        <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); 
                    padding: 30px; border-radius: 15px; color: white; text-align: center; margin: 20px;">
            <h3>🎨 AI图片编辑器</h3>
            <p>专业的AI驱动图片处理工具</p>
        </div>
        """, unsafe_allow_html=True)
        
        st.markdown("### 🎯 主要功能")
        st.markdown("""
        - 🔄 **AI换装**：智能服装替换技术
        - 🖼️ **背景去除**：一键移除图片背景
        - ⚪ **白底添加**：专业证件照背景
        - ⚡ **快速处理**：即传即用，无需注册
        - 🔒 **隐私保护**：本地处理，数据安全
        - 📥 **即时下载**：处理完成立即下载
        """)
        
        if st.button("🎨 进入AI图片编辑器", type="primary", use_container_width=True):
            st.switch_page("photo_editor.py")
    
    # 特性对比
    st.markdown("---")
    st.markdown("### 📊 功能对比")
    
    comparison_data = {
        "功能": ["目标用户", "使用场景", "数据管理", "处理方式", "输出结果"],
        "球队管理系统": [
            "球队管理员、教练",
            "比赛报名、球员管理",
            "持久化存储",
            "批量处理",
            "完整报名表"
        ],
        "AI图片编辑器": [
            "任何需要修图的用户",
            "快速修图、图片美化",
            "临时处理",
            "单张处理",
            "处理后图片"
        ]
    }
    
    st.table(comparison_data)
    
    # 底部信息
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #888; padding: 20px;">
        <p>💡 <strong>提示</strong>：两个应用程序完全独立，您可以根据需要选择使用</p>
        <p>🔧 技术支持 | 📞 联系我们 | 📖 使用文档</p>
    </div>
    """, unsafe_allow_html=True)


def main():
    """主函数"""
    configure_multipage()
    render_welcome_page()


if __name__ == "__main__":
    main()
