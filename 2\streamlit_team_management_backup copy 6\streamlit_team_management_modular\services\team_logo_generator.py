#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI队徽生成服务
AI Team Logo Generator Service

提供完整的AI队徽生成功能，包括描述生成和图像生成
"""

import os
import time
import requests
from typing import Dict, Any, Optional
from datetime import datetime
from PIL import Image
import streamlit as st

from config.settings import app_settings
from utils.smart_cache_manager import cache_important


class TeamLogoGenerator:
    """AI队徽生成器"""
    
    def __init__(self):
        """初始化队徽生成器"""
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.logo_folder = os.path.join("data", "team_logos")
        self._ensure_logo_folder()
    
    def _ensure_logo_folder(self):
        """确保队徽文件夹存在"""
        if not os.path.exists(self.logo_folder):
            os.makedirs(self.logo_folder, exist_ok=True)
    
    def generate_team_logo_complete(self, team_name: str, team_style: str = "现代", 
                                  color_preference: str = "蓝色和白色") -> Dict[str, Any]:
        """
        生成完整的队徽（描述 + 图像）
        
        Args:
            team_name: 球队名称
            team_style: 队徽风格（现代、传统、简约、复古）
            color_preference: 颜色偏好
            
        Returns:
            Dict[str, Any]: 生成结果
        """
        try:
            st.info("🎨 开始生成AI队徽...")
            
            # 步骤1: 生成队徽描述
            st.info("📝 第1步: 生成队徽设计描述...")
            description_result = self._generate_logo_description(team_name, team_style, color_preference)
            
            if not description_result.get("success"):
                return description_result
            
            logo_description = description_result["description"]
            st.success("✅ 队徽描述生成完成")
            
            # 步骤2: 生成队徽图像
            st.info("🖼️ 第2步: 生成队徽图像...")
            image_result = self._generate_logo_image(team_name, logo_description)
            
            if not image_result.get("success"):
                return {
                    "success": False,
                    "error": image_result.get("error"),
                    "description": logo_description,  # 至少返回描述
                    "has_image": False
                }
            
            st.success("✅ 队徽图像生成完成")
            
            # 步骤3: 保存和返回结果
            result = {
                "success": True,
                "team_name": team_name,
                "description": logo_description,
                "image_path": image_result["image_path"],
                "image_url": image_result.get("image_url"),
                "has_image": True,
                "style": team_style,
                "colors": color_preference,
                "generated_at": datetime.now().isoformat(),
                "message": "队徽生成成功！"
            }
            
            # 缓存结果
            self._cache_logo_result(team_name, result)
            
            return result
            
        except Exception as e:
            st.error(f"❌ 队徽生成失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "has_image": False
            }
    
    def _generate_logo_description(self, team_name: str, team_style: str, 
                                 color_preference: str) -> Dict[str, Any]:
        """生成队徽描述"""
        try:
            if not self.openai_api_key:
                return {
                    "success": False,
                    "error": "OpenAI API密钥未配置"
                }
            
            import openai
            client = openai.OpenAI(api_key=self.openai_api_key)
            
            prompt = f"""
            请为足球队"{team_name}"设计一个队徽描述。

            要求：
            - 风格：{team_style}
            - 颜色偏好：{color_preference}
            - 适合足球队使用
            - 简洁明了，易于识别
            - 体现团队精神

            请提供详细的设计描述，包括：
            1. 主要图案元素
            2. 颜色搭配
            3. 整体布局
            4. 寓意说明
            """
            
            response = client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "你是一个专业的队徽设计师，擅长为足球队设计有意义的队徽。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.8
            )
            
            description = response.choices[0].message.content.strip()
            
            return {
                "success": True,
                "description": description
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"生成描述失败: {e}"
            }
    
    def _generate_logo_image(self, team_name: str, description: str) -> Dict[str, Any]:
        """生成队徽图像"""
        try:
            if not self.openai_api_key:
                return {
                    "success": False,
                    "error": "OpenAI API密钥未配置"
                }
            
            import openai
            client = openai.OpenAI(api_key=self.openai_api_key)
            
            # 构建DALL-E提示词
            dalle_prompt = f"Design a football team logo: {description}"
            
            # 调用DALL-E 3生成图像
            response = client.images.generate(
                model="dall-e-3",
                prompt=dalle_prompt,
                size="1024x1024",
                quality="standard",
                n=1
            )
            
            image_url = response.data[0].url
            
            # 下载并保存图像
            image_path = self._download_and_save_image(image_url, team_name)
            
            return {
                "success": True,
                "image_url": image_url,
                "image_path": image_path
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"生成图像失败: {e}"
            }
    
    def _download_and_save_image(self, image_url: str, team_name: str) -> str:
        """下载并保存图像"""
        try:
            # 生成文件名
            timestamp = int(time.time())
            safe_team_name = "".join(c for c in team_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_team_name = safe_team_name.replace(' ', '_')
            filename = f"{safe_team_name}_logo_{timestamp}.png"
            file_path = os.path.join(self.logo_folder, filename)
            
            # 下载图像
            response = requests.get(image_url, timeout=30)
            response.raise_for_status()
            
            # 保存图像
            with open(file_path, 'wb') as f:
                f.write(response.content)
            
            # 验证图像
            try:
                with Image.open(file_path) as img:
                    img.verify()
            except Exception:
                os.remove(file_path)
                raise Exception("下载的图像文件损坏")
            
            return file_path
            
        except Exception as e:
            raise Exception(f"保存图像失败: {e}")
    
    def _cache_logo_result(self, team_name: str, result: Dict[str, Any]) -> None:
        """缓存队徽生成结果"""
        pass
    
    def get_cached_logo(self, team_name: str) -> Optional[Dict[str, Any]]:
        """获取缓存的队徽"""
        # 这里可以实现从缓存获取逻辑
        return None
    
    def list_generated_logos(self) -> list:
        """列出所有生成的队徽"""
        try:
            if not os.path.exists(self.logo_folder):
                return []
            
            logos = []
            for filename in os.listdir(self.logo_folder):
                if filename.endswith(('.png', '.jpg', '.jpeg')):
                    file_path = os.path.join(self.logo_folder, filename)
                    file_stat = os.stat(file_path)
                    
                    logos.append({
                        "filename": filename,
                        "path": file_path,
                        "size": file_stat.st_size,
                        "created_at": datetime.fromtimestamp(file_stat.st_ctime).isoformat()
                    })
            
            return sorted(logos, key=lambda x: x["created_at"], reverse=True)
            
        except Exception as e:
            st.error(f"获取队徽列表失败: {e}")
            return []
    
    def delete_logo(self, file_path: str) -> bool:
        """删除队徽文件"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
            return False
        except Exception as e:
            st.error(f"删除队徽失败: {e}")
            return False


# 全局实例
team_logo_generator = TeamLogoGenerator()
