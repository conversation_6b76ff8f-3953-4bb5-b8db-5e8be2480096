#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动脚本
Launch Script

用于启动球队管理系统的便捷脚本
"""

import os
import sys
import subprocess


def check_requirements():
    """检查依赖是否安装"""
    try:
        import streamlit
        import PIL
        import pandas
        print("✅ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False


def create_directories():
    """创建必要的目录"""
    directories = ['data', 'uploads', 'ai_export', 'processed_photos']
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ 创建目录: {directory}")
        else:
            print(f"📁 目录已存在: {directory}")


def main():
    """主函数"""
    print("🚀 启动球队管理系统...")
    print("=" * 50)
    
    # 检查依赖
    if not check_requirements():
        sys.exit(1)
    
    # 创建目录
    create_directories()
    
    print("\n🎯 启动Streamlit应用...")
    print("=" * 50)
    
    # 启动Streamlit应用
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.port", "8501",
            "--server.address", "localhost"
        ])
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")


if __name__ == "__main__":
    main()
