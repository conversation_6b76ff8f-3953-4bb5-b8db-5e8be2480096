# Imagine（绘画）

## OpenAPI Specification

```yaml
openapi: 3.0.1
info:
  title: ''
  description: ''
  version: 1.0.0
paths:
  /mj/submit/imagine:
    post:
      summary: Imagine（绘画）
      deprecated: false
      description: '**价格：0.05PTC/次**'
      operationId: imagineUsingPOST
      tags:
        - 图片生成/Midjourney
        - 任务提交
      parameters:
        - name: mj-api-secret
          in: header
          description: 302.AI后台的API Key
          required: true
          example: '{{YOUR_API_KEY}}'
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              x-apifox-refs: {}
              x-apifox-orders:
                - base64Array
                - botType
                - notifyHook
                - prompt
                - state
              properties:
                base64Array:
                  type: array
                  description: 垫图base64数组
                  items:
                    type: string
                botType:
                  type: string
                  description: bot类型，mj(默认)或niji
                  enum:
                    - MID_JOURNEY
                    - NIJI_JOURNEY
                  examples:
                    - MID_JOURNEY
                notifyHook:
                  type: string
                  description: 回调地址, 为空时使用全局notifyHook
                prompt:
                  type: string
                  description: 提示词
                  examples:
                    - Cat
                state:
                  type: string
                  description: 自定义参数
              required:
                - prompt
              x-apifox-ignore-properties: []
            example:
              base64Array: []
              botType: MID_JOURNEY
              notifyHook: ''
              prompt: Cat
              state: ''
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/%E6%8F%90%E4%BA%A4%E7%BB%93%E6%9E%9C'
          headers: {}
          x-apifox-name: OK
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties: {}
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          headers: {}
          x-apifox-name: Created
        '401':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties: {}
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          headers: {}
          x-apifox-name: Unauthorized
        '403':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties: {}
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          headers: {}
          x-apifox-name: Forbidden
        '404':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties: {}
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          headers: {}
          x-apifox-name: Not Found
      security: []
      x-apifox-folder: 图片生成/Midjourney
      x-apifox-status: released
      x-run-in-apifox: https://app.apifox.com/web/project/4012774/apis/api-160578879-run
components:
  schemas:
    提交结果:
      type: object
      required:
        - code
        - description
      properties:
        code:
          type: integer
          format: int32
          description: '状态码: 1(提交成功), 22(排队中), other(错误)'
          examples:
            - 1
        description:
          type: string
          description: 描述
          examples:
            - 提交成功
        properties:
          type: object
          description: 扩展字段
          x-apifox-orders: []
          properties: {}
          x-apifox-ignore-properties: []
        result:
          type: string
          description: 任务ID
          examples:
            - 1320098173412546
      title: 提交结果
      x-apifox-orders:
        - code
        - description
        - properties
        - result
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
  securitySchemes: {}
servers:
  - url: https://api.302.ai
    description: 正式环境
  - url: https://api.302ai.cn
    description: 国内中转
security: []

```

import requests
import json

url = "https://api.302.ai/mj/submit/imagine"

payload = json.dumps({
   "base64Array": [],
   "botType": "MID_JOURNEY",
   "notifyHook": "",
   "prompt": "Cat",
   "state": ""
})
headers = {
   'mj-api-secret': '',
   'Content-Type': 'application/json'
}

response = requests.request("POST", url, headers=headers, data=payload)

print(response.text)

import http.client
import json

conn = http.client.HTTPSConnection("api.302.ai")
payload = json.dumps({
   "base64Array": [],
   "botType": "MID_JOURNEY",
   "notifyHook": "",
   "prompt": "Cat",
   "state": ""
})
headers = {
   'mj-api-secret': '',
   'Content-Type': 'application/json'
}
conn.request("POST", "/mj/submit/imagine", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))


import http.client
import json

conn = http.client.HTTPSConnection("api.302.ai")
payload = json.dumps({
   "base64Array": [],
   "botType": "MID_JOURNEY",
   "notifyHook": "",
   "prompt": "Cat",
   "state": ""
})
headers = {
   'mj-api-secret': '',
   'Content-Type': 'application/json'
}
conn.request("POST", "/mj/submit/imagine", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))

import requests
import json

url = "https://api.302.ai/mj/submit/imagine"

payload = json.dumps({
   "base64Array": [],
   "botType": "MID_JOURNEY",
   "notifyHook": "",
   "prompt": "Cat",
   "state": ""
})
headers = {
   'mj-api-secret': '',
   'Content-Type': 'application/json'
}

response = requests.request("POST", url, headers=headers, data=payload)

print(response.text)