#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI聊天组件
AI Chat Component

提供AI球队信息收集助手的UI组件
"""

import streamlit as st
import json
from typing import List, Dict, Any
from datetime import datetime

from services.ai_service import AIService
from services.team_service import TeamService
from services.player_service import PlayerService
from services.auth_service import AuthService
from components.word_generator import create_word_generator_component


class AIChatComponent:
    """AI聊天组件（支持函数调用和结构化输出）"""

    def __init__(self):
        # 获取用户ID
        auth_service = AuthService()
        self.user_id = auth_service.get_current_user_id()

        # 初始化服务（传入用户ID）
        self.ai_service = AIService(self.user_id)
        self.team_service = TeamService()
        self.player_service = PlayerService()

        # 延迟初始化（避免循环导入）
        self.data_bridge = None
        self.fashion_workflow = None
        self.word_generator = None

        # 对话状态管理
        self.conversation_history = []
        self.current_team_id = None

    def _init_workflow_components(self):
        """延迟初始化工作流组件"""
        if self.data_bridge is None:
            try:
                from services.data_bridge_service import DataBridgeService
                self.data_bridge = DataBridgeService(self.user_id)
            except Exception as e:
                st.warning(f"数据桥接服务初始化失败: {e}")
                self.data_bridge = None

        if self.word_generator is None:
            try:
                self.word_generator = create_word_generator_component()
            except Exception as e:
                st.warning(f"Word生成器初始化失败: {e}")
                self.word_generator = None

        if self.fashion_workflow is None:
            try:
                from components.fashion_workflow import FashionWorkflowComponent
                self.fashion_workflow = FashionWorkflowComponent()
            except Exception as e:
                st.warning(f"换装工作流组件初始化失败: {e}")
                self.fashion_workflow = None
    
    def initialize_chat(self, team_name: str) -> None:
        """
        初始化聊天

        Args:
            team_name: 球队名称
        """
        # 获取当前球队统计信息
        current_team_stats = self.team_service.get_team_stats(team_name)

        # 临时调试信息 - 总是显示
        st.write(f"🔍 AI聊天组件调试信息:")
        st.write(f"   当前球队: {team_name}")
        st.write(f"   当前统计: {current_team_stats}")
        st.write(f"   上次统计: {st.session_state.get('last_team_stats', '未设置')}")
        st.write(f"   最近操作: {st.session_state.get('last_player_action', '无')}")
        st.write(f"   AI消息存在: {'ai_messages' in st.session_state}")
        # 显示缓存清理信息
        if 'cache_cleared_info' in st.session_state:
            st.write(f"   {st.session_state.cache_cleared_info}")

        # 检查是否需要初始化或更新AI上下文
        if "ai_messages" not in st.session_state:
            # 首次初始化
            st.write(f"🔍 首次初始化AI聊天")
            st.session_state.ai_messages = self.ai_service.initialize_chat_messages(team_name, current_team_stats)
            st.session_state.last_team_stats = current_team_stats.copy()
            st.session_state.last_team_name = team_name
        else:
            # 检查球队是否切换或状态是否发生变化
            last_team_name = st.session_state.get('last_team_name', '')
            last_stats = st.session_state.get('last_team_stats', {})

            st.write(f"🔍 检查状态变化:")
            st.write(f"   上次球队: {last_team_name}")
            st.write(f"   当前球队: {team_name}")
            st.write(f"   球队是否切换: {last_team_name != team_name}")

            # 如果切换了球队，重新初始化
            if last_team_name != team_name:
                st.write(f"🔍 球队切换，重新初始化")
                st.session_state.ai_messages = self.ai_service.initialize_chat_messages(team_name, current_team_stats)
                st.session_state.last_team_stats = current_team_stats.copy()
                st.session_state.last_team_name = team_name
            # 如果球队状态发生变化，自动刷新AI上下文
            else:
                has_changed = self._has_team_stats_changed(last_stats, current_team_stats)
                st.write(f"🔍 状态是否变化: {has_changed}")

                if has_changed:
                    st.write(f"🔍 检测到状态变化！正在刷新AI上下文")
                    self._auto_refresh_context(team_name, current_team_stats)
                    st.session_state.last_team_stats = current_team_stats.copy()
                else:
                    st.write(f"🔍 状态未变化，不刷新AI上下文")

        # 设置默认模型
        if "openai_model" not in st.session_state:
            st.session_state["openai_model"] = "gpt-4o"

    def refresh_chat_context(self, team_name: str) -> None:
        """
        刷新聊天上下文（当球队状态发生变化时调用）

        Args:
            team_name: 球队名称
        """
        # 获取最新的球队统计信息
        team_stats = self.team_service.get_team_stats(team_name)

        # 如果已有聊天记录，只更新系统消息
        if "ai_messages" in st.session_state and len(st.session_state.ai_messages) > 0:
            # 更新系统提示词
            new_system_prompt = self.ai_service.get_system_prompt(team_name, team_stats)
            st.session_state.ai_messages[0] = {"role": "system", "content": new_system_prompt}

            # 添加状态更新提示
            status_update = self._generate_status_update_message(team_stats)
            if status_update:
                st.session_state.ai_messages.append({
                    "role": "assistant",
                    "content": status_update
                })
        else:
            # 重新初始化聊天
            st.session_state.ai_messages = self.ai_service.initialize_chat_messages(team_name, team_stats)

    def _generate_status_update_message(self, team_stats: Dict[str, Any]) -> str:
        """
        生成状态更新消息

        Args:
            team_stats: 球队统计信息

        Returns:
            str: 状态更新消息，如果不需要更新则返回空字符串
        """
        total_players = team_stats['total_players']
        completion_rate = team_stats['completion_rate']

        # 简单的状态更新提示
        if total_players > 0 and completion_rate == 100:
            return "🎉 太好了！您的球队信息已经完整，现在可以生成完整的报名表了。"
        elif total_players > 0:
            return f"📊 球队状态已更新：当前有{total_players}名球员，完成度{completion_rate:.1f}%。"

        return ""

    def _has_team_stats_changed(self, last_stats: Dict[str, Any], current_stats: Dict[str, Any]) -> bool:
        """
        检查球队状态是否发生变化

        Args:
            last_stats: 上次的统计信息
            current_stats: 当前的统计信息

        Returns:
            bool: 是否发生变化
        """
        # 检查关键指标是否变化
        key_fields = ['total_players', 'players_with_photos', 'completion_rate']

        for field in key_fields:
            if last_stats.get(field, 0) != current_stats.get(field, 0):
                return True

        return False

    def _auto_refresh_context(self, team_name: str, current_stats: Dict[str, Any]) -> None:
        """
        自动刷新AI上下文

        Args:
            team_name: 球队名称
            current_stats: 当前统计信息
        """
        # 更新系统提示词
        new_system_prompt = self.ai_service.get_system_prompt(team_name, current_stats)
        if "ai_messages" in st.session_state and len(st.session_state.ai_messages) > 0:
            st.session_state.ai_messages[0] = {"role": "system", "content": new_system_prompt}

            # 生成基于具体操作的个性化状态更新消息
            status_update = self._generate_action_based_update(current_stats)
            if status_update:
                st.session_state.ai_messages.append({
                    "role": "assistant",
                    "content": status_update
                })

    def _generate_auto_status_update(self, team_stats: Dict[str, Any]) -> str:
        """
        生成自动状态更新消息

        Args:
            team_stats: 球队统计信息

        Returns:
            str: 状态更新消息
        """
        total_players = team_stats['total_players']
        completion_rate = team_stats['completion_rate']

        if total_players == 1:
            return "🎉 太好了！您已经添加了第一名球员。现在可以继续添加更多球员，或者我们可以开始收集比赛信息。"
        elif total_players > 1 and completion_rate == 100:
            return f"✨ 完美！您的球队现在有{total_players}名球员，所有信息都已完整。我们可以直接生成完整的报名表了！"
        elif total_players > 1:
            return f"📈 球队更新：现在有{total_players}名球员，完成度{completion_rate:.1f}%。继续添加球员或补充照片吧！"

        return ""

    def _generate_action_based_update(self, team_stats: Dict[str, Any]) -> str:
        """
        根据具体操作生成个性化状态更新消息

        Args:
            team_stats: 球队统计信息

        Returns:
            str: 个性化状态更新消息
        """
        # 检查是否有最近的球员操作记录
        last_action = st.session_state.get('last_player_action')
        if not last_action:
            # 如果没有具体操作记录，使用通用消息
            return self._generate_auto_status_update(team_stats)

        action_type = last_action.get('type')
        details = last_action.get('details', {})
        total_players = team_stats['total_players']
        completion_rate = team_stats['completion_rate']

        if action_type == "add_player":
            name = details.get('name', '新球员')
            jersey_number = details.get('jersey_number', '')
            has_photo = details.get('has_photo', False)

            message = f"🎉 太好了！您刚刚添加了球员「{name}」"
            if jersey_number:
                message += f"（{jersey_number}号）"

            if has_photo:
                message += "，并且已经上传了照片。"
            else:
                message += "，建议稍后为该球员上传照片。"

            message += f"\n\n📊 当前球队状态：{total_players}名球员，完成度{completion_rate:.1f}%"

            if total_players == 1:
                message += "\n💡 这是您的第一名球员！继续添加更多球员，或者我们可以开始收集比赛信息。"
            elif total_players >= 5:
                message += "\n⚽ 球员数量已经足够参加正式比赛了！"
            else:
                message += f"\n💪 建议至少准备5名球员参赛，还需要{5-total_players}名球员。"

        elif action_type == "delete_player":
            name = details.get('name', '球员')
            jersey_number = details.get('jersey_number', '')

            message = f"🗑️ 已删除球员「{name}」"
            if jersey_number:
                message += f"（{jersey_number}号）"
            message += "。"

            message += f"\n\n📊 当前球队状态：{total_players}名球员，完成度{completion_rate:.1f}%"

            if total_players == 0:
                message += "\n💡 球队现在没有球员了，建议重新添加球员信息。"
            elif total_players < 5:
                message += f"\n💪 建议至少准备5名球员参赛，还需要{5-total_players}名球员。"

        elif action_type == "update_photo":
            name = details.get('name', '球员')
            jersey_number = details.get('jersey_number', '')

            message = f"📸 已为球员「{name}」"
            if jersey_number:
                message += f"（{jersey_number}号）"
            message += "更新了照片。"

            message += f"\n\n📊 当前球队状态：{total_players}名球员，完成度{completion_rate:.1f}%"

            if completion_rate == 100:
                message += "\n✨ 完美！所有球员信息都已完整，可以生成完整的报名表了！"
            else:
                players_with_photos = team_stats['players_with_photos']
                missing_photos = total_players - players_with_photos
                message += f"\n📷 还有{missing_photos}名球员需要上传照片。"
        else:
            # 未知操作类型，使用通用消息
            return self._generate_auto_status_update(team_stats)

        # 清除操作记录，避免重复显示
        if 'last_player_action' in st.session_state:
            del st.session_state.last_player_action

        return message
    
    def render_chat_interface(self, team_name: str) -> None:
        """
        渲染聊天界面
        
        Args:
            team_name: 球队名称
        """
        if not self.ai_service.is_available():
            st.warning("AI服务不可用，请检查OpenAI API密钥配置")
            return
        
        st.markdown("### 🤖 AI球队信息收集助手")
        
        # 初始化聊天
        self.initialize_chat(team_name)
        
        # 创建AI对话容器
        chat_container = st.container()
        with chat_container:
            # 显示对话历史（跳过系统消息）
            for message in st.session_state.ai_messages:
                if message["role"] != "system":
                    with st.chat_message(message["role"]):
                        st.markdown(message["content"])
        
        # 在AI对话区域正下方放置输入框
        with st.container():
            # 用户输入
            if prompt := st.chat_input("请输入您的回复..."):
                # 添加用户消息
                st.session_state.ai_messages.append({"role": "user", "content": prompt})
                
                # 显示用户消息
                with chat_container:
                    with st.chat_message("user"):
                        st.markdown(prompt)
                
                # 生成AI回复
                with chat_container:
                    with st.chat_message("assistant"):
                        with st.spinner("🤖 AI正在思考..."):
                            # 使用增强功能处理消息
                            response = self.process_enhanced_message(prompt, team_name)
                        st.markdown(response)

                # 添加AI回复到历史
                st.session_state.ai_messages.append({"role": "assistant", "content": response})
                
                st.rerun()
    
    def render_chat_controls(self, team_name: str = None) -> None:
        """
        渲染聊天控制按钮

        Args:
            team_name: 球队名称（保留参数兼容性，但不再需要手动刷新）
        """
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("🔄 重新开始对话"):
                if "ai_messages" in st.session_state:
                    del st.session_state.ai_messages
                if "last_team_stats" in st.session_state:
                    del st.session_state.last_team_stats
                st.rerun()

        with col2:
            if st.button("📋 提取信息"):
                self._extract_team_info()

        with col3:
            if st.button("📥 导出对话"):
                self._export_chat_history()

        # Word生成面板
        self._render_word_generation_panel(team_name)
    
    def _extract_team_info(self) -> None:
        """提取球队信息"""
        if "ai_messages" not in st.session_state:
            st.warning("没有对话历史")
            return
        
        extracted_info = self.ai_service.extract_team_info(st.session_state.ai_messages)
        missing_info = self.ai_service.validate_extracted_info(extracted_info)
        
        st.markdown("### 📋 提取的信息")
        
        if missing_info:
            st.warning(f"缺少以下信息：{', '.join(missing_info)}")
        else:
            st.success("✅ 信息收集完整")
        
        # 显示提取的信息
        summary = self.ai_service.generate_summary(extracted_info)
        st.markdown(summary)
        
        # 保存到session state
        st.session_state.extracted_team_info = extracted_info
    
    def _export_chat_history(self) -> None:
        """导出对话历史"""
        if "ai_messages" not in st.session_state:
            st.warning("没有对话历史")
            return
        
        # 过滤掉系统消息
        chat_history = [
            msg for msg in st.session_state.ai_messages 
            if msg["role"] != "system"
        ]
        
        # 格式化对话历史
        formatted_history = []
        for msg in chat_history:
            role = "用户" if msg["role"] == "user" else "AI助手"
            formatted_history.append(f"**{role}**: {msg['content']}\n")
        
        chat_text = "\n".join(formatted_history)
        
        st.download_button(
            label="📥 下载对话记录",
            data=chat_text,
            file_name=f"team_chat_history_{st.session_state.get('current_team', 'default')}.txt",
            mime="text/plain"
        )
    
    def get_extracted_info(self) -> Dict[str, Any]:
        """
        获取提取的信息
        
        Returns:
            Dict[str, Any]: 提取的球队信息
        """
        return st.session_state.get('extracted_team_info', {})
    
    def has_chat_history(self) -> bool:
        """
        检查是否有聊天历史
        
        Returns:
            bool: 是否有聊天历史
        """
        return "ai_messages" in st.session_state and len(st.session_state.ai_messages) > 2
    
    def render_info_summary(self) -> None:
        """渲染信息总结"""
        extracted_info = self.get_extracted_info()
        
        if extracted_info:
            st.markdown("### 📋 收集的信息总结")
            summary = self.ai_service.generate_summary(extracted_info)
            st.markdown(summary)
            
            # 验证信息完整性
            missing_info = self.ai_service.validate_extracted_info(extracted_info)
            if missing_info:
                st.warning(f"⚠️ 还需要收集：{', '.join(missing_info)}")
            else:
                st.success("✅ 信息收集完整，可以生成报名表")
    
    def clear_chat(self) -> None:
        """清除聊天记录"""
        if "ai_messages" in st.session_state:
            del st.session_state.ai_messages
        if "extracted_team_info" in st.session_state:
            del st.session_state.extracted_team_info

    def render_fashion_workflow_integration(self, team_name: str) -> None:
        """
        渲染换装工作流集成界面

        Args:
            team_name: 当前球队名称
        """
        try:
            # 延迟初始化工作流组件
            self._init_workflow_components()

            # 智能检测场景二：AI收集信息后的自动提示
            self._check_ai_scenario_readiness(team_name)

            # 检查是否应该显示换装功能
            if self.fashion_workflow and self.fashion_workflow.should_show_fashion_trigger(team_name):
                st.markdown("---")

                # 智能检测是否可以自动同步数据
                self._check_auto_sync_opportunity(team_name)

                # 渲染换装触发器
                self.fashion_workflow.render_fashion_trigger(team_name)
        except Exception as e:
            st.warning(f"换装工作流集成失败: {e}")
            # 显示基本的换装提示
            st.info("💡 换装功能暂时不可用，请稍后重试")

    def _check_auto_sync_opportunity(self, team_name: str) -> None:
        """检查是否有自动同步数据的机会"""
        try:
            # 检查数据桥接服务是否可用
            if not self.data_bridge:
                return

            # 检查是否有聊天历史
            if not self.has_chat_history():
                return

            # 检查是否已经同步过
            sync_key = f"auto_sync_done_{team_name}"
            if st.session_state.get(sync_key, False):
                return

            # 检查聊天内容是否包含球员信息
            chat_messages = st.session_state.get("ai_messages", [])
            if self._contains_player_info(chat_messages):
                # 显示自动同步提示
                with st.expander("🔄 智能数据同步", expanded=True):
                    st.info("💡 检测到您在聊天中提到了球员信息，是否要自动同步到球队数据？")

                    col1, col2 = st.columns(2)
                    with col1:
                        if st.button("🚀 自动同步", type="primary", key=f"auto_sync_{team_name}"):
                            self._execute_auto_sync(team_name, chat_messages)
                            st.session_state[sync_key] = True
                            st.rerun()

                    with col2:
                        if st.button("❌ 跳过", key=f"skip_sync_{team_name}"):
                            st.session_state[sync_key] = True
                            st.rerun()

        except Exception as e:
            st.error(f"检查自动同步机会失败: {e}")

    def _contains_player_info(self, chat_messages: List[Dict[str, str]]) -> bool:
        """检查聊天内容是否包含球员信息"""
        try:
            conversation_text = " ".join([
                msg["content"] for msg in chat_messages
                if msg["role"] in ["user", "assistant"]
            ])

            # 简单的关键词检测
            player_keywords = ["球员", "队员", "成员", "名单", "阵容", "号码"]
            return any(keyword in conversation_text for keyword in player_keywords)

        except Exception:
            return False

    def _execute_auto_sync(self, team_name: str, chat_messages: List[Dict[str, str]]) -> None:
        """执行自动同步"""
        try:
            if not self.data_bridge:
                st.error("❌ 数据桥接服务不可用")
                return

            with st.spinner("🔄 正在同步聊天数据到球队系统..."):
                sync_result = self.data_bridge.sync_chat_to_team_data(team_name, chat_messages)

            if sync_result["success"]:
                sync_info = sync_result["sync_results"]
                st.success("✅ 数据同步成功！")

                # 显示同步结果
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("新增球员", sync_info["players_added"])
                with col2:
                    st.metric("更新球员", sync_info["players_updated"])
                with col3:
                    st.metric("导出创建", "✅" if sync_info["export_created"] else "❌")

                # 如果创建了导出，提示可以开始换装
                if sync_info["export_created"]:
                    st.info("🎨 数据已准备就绪，可以开始自动换装！")
            else:
                st.error(f"❌ 数据同步失败: {sync_result['error']}")

        except Exception as e:
            st.error(f"执行自动同步失败: {e}")

    def _check_ai_scenario_readiness(self, team_name: str) -> None:
        """检查统一场景的准备状态并提供智能提示"""
        try:
            # 检查是否有聊天历史且包含球员信息
            if not self.has_chat_history():
                return

            chat_messages = st.session_state.get("ai_messages", [])
            if not self._contains_player_info(chat_messages):
                return

            # 检查是否已经提取了信息
            extracted_info = self.get_extracted_info()

            # 检查球员数据状态
            team_stats = self.team_service.get_team_stats(team_name)
            has_players = team_stats.get('total_players', 0) > 0
            has_photos = team_stats.get('players_with_photos', 0) > 0

            # 统一场景的智能提示逻辑
            scenario_key = f"unified_scenario_check_{team_name}"
            if st.session_state.get(scenario_key, False):
                return  # 已经处理过了

            # 统一场景：必须同时满足AI数据和照片两个条件
            if extracted_info and has_players and has_photos:
                # 完全准备就绪 - 自动提示换装
                with st.expander("🎉 统一换装模式 - 准备完成！", expanded=True):
                    st.success("✨ 太棒了！AI已收集球队数据，球员照片也已上传完成")
                    st.info("🎨 现在可以开始自动换装了！请查看下方的换装功能")
                    st.info(f"📊 完整状态：AI数据 ✅ + {team_stats['players_with_photos']}/{team_stats['total_players']} 名球员有照片")

                    if st.button("🚀 立即开始换装", type="primary", key=f"unified_ready_fashion_{team_name}"):
                        st.session_state[scenario_key] = True
                        # 滚动到换装区域的提示
                        st.info("👇 请查看下方的自动换装功能")
                        st.rerun()

            elif extracted_info and has_players and not has_photos:
                # AI已收集信息，有球员数据，但缺少照片
                with st.expander("📸 统一换装模式 - 需要上传照片", expanded=True):
                    st.success("✅ AI已成功收集球队数据")
                    st.warning("📷 现在需要上传球员照片来完成准备")
                    st.info(f"📊 当前状态：AI数据 ✅ + {team_stats['players_with_photos']}/{team_stats['total_players']} 张照片")
                    st.markdown("**⚠️ 重要：统一模式需要AI数据 + 照片两个条件都满足**")

                    col1, col2 = st.columns(2)
                    with col1:
                        if st.button("📤 批量上传照片", type="primary", key=f"unified_upload_{team_name}"):
                            st.session_state.batch_mode = 'upload'
                            st.session_state[scenario_key] = True
                            st.rerun()

                    with col2:
                        if st.button("➕ 单个添加", key=f"unified_single_add_{team_name}"):
                            st.session_state.show_add_form = True
                            st.session_state[scenario_key] = True
                            st.rerun()

            elif extracted_info and not has_players:
                # AI收集了信息但还没有保存到球员系统
                with st.expander("🔄 统一换装模式 - 数据同步", expanded=True):
                    st.info("🤖 AI已收集球队数据，但还需要同步到球员管理系统")
                    st.markdown("**下一步操作：**")
                    st.markdown("1. 点击上方的'📋 提取信息'按钮")
                    st.markdown("2. 或者继续与AI对话，让AI自动保存球员信息")
                    st.markdown("3. 然后上传球员照片")
                    st.warning("⚠️ 统一模式需要完成数据同步和照片上传两个步骤")

                    if st.button("🔄 自动同步数据", type="primary", key=f"unified_sync_{team_name}"):
                        self._execute_auto_sync(team_name, chat_messages)
                        st.session_state[scenario_key] = True
                        st.rerun()

            elif not extracted_info and has_players and has_photos:
                # 有照片但没有AI数据
                with st.expander("🤖 统一换装模式 - 需要AI数据", expanded=True):
                    st.success("✅ 球员照片已上传完成")
                    st.warning("🤖 现在需要AI收集球队数据来完成准备")
                    st.info(f"📊 当前状态：{team_stats['players_with_photos']}/{team_stats['total_players']} 张照片 + AI数据 ❌")
                    st.markdown("**⚠️ 重要：统一模式需要AI数据 + 照片两个条件都满足**")

                    st.markdown("""
                    **请在AI聊天中输入球队信息：**
                    - 球队名称和基本信息
                    - 球员名单和详细信息
                    - 比赛相关信息
                    """)

                    st.code('示例：我们是XX足球队，队员有张三、李四、王五...')
                    st.info("💬 请在上方AI聊天区域输入球队信息")

            elif not extracted_info:
                # AI聊天中提到了球员信息，但还没有提取
                with st.expander("📋 统一换装模式 - 提取信息", expanded=True):
                    st.info("🤖 检测到您在聊天中提到了球员信息")
                    st.markdown("**下一步操作：**")
                    st.markdown("1. 点击'📋 提取信息'按钮让AI整理数据")
                    st.markdown("2. 上传球员照片")
                    st.markdown("3. 完成后即可自动换装")
                    st.warning("⚠️ 统一模式需要AI数据 + 照片两个条件都满足")

        except Exception as e:
            # 静默处理错误，不影响主要功能
            pass

    def process_enhanced_message(self, user_message: str, team_name: str) -> str:
        """
        处理增强消息（支持函数调用）

        Args:
            user_message: 用户消息
            team_name: 当前球队名称

        Returns:
            str: AI回复
        """
        if not self.ai_service.has_enhanced_features():
            # 回退到基础功能
            return self._process_basic_message(user_message, team_name)

        try:
            # 构建对话历史
            messages = self._build_conversation_history(team_name)
            messages.append({"role": "user", "content": user_message})

            # 使用增强AI功能
            ai_response, function_results = self.ai_service.enhanced_service.generate_response_with_functions(
                messages, use_structured_output=True
            )

            # 处理函数调用结果
            if function_results:
                self._handle_function_results(function_results, team_name)

                # 🎨 检查是否有自动生成的队徽
                self._handle_auto_generated_logos(function_results)

                # 显示函数调用结果
                with st.expander("🔧 AI执行的操作", expanded=False):
                    for i, result in enumerate(function_results, 1):
                        st.write(f"**操作 {i}:**")
                        if result.get("success"):
                            st.success(f"✅ {result.get('message', '操作成功')}")
                        else:
                            st.error(f"❌ {result.get('error', '操作失败')}")

                        # 显示详细结果
                        if result.get("extracted_info"):
                            st.json(result["extracted_info"])

            return ai_response

        except Exception as e:
            st.error(f"增强AI功能失败: {e}")
            return self._process_basic_message(user_message, team_name)

    def _handle_auto_generated_logos(self, function_results: List[Dict[str, Any]]) -> None:
        """处理自动生成的队徽"""
        try:
            for result in function_results:
                # 检查是否有自动生成的队徽
                extracted_info = result.get("extracted_info", {})
                auto_logo = extracted_info.get("auto_generated_logo")

                if auto_logo and auto_logo.get("generated"):
                    st.success("🎨 AI已自动为您生成队徽！")

                    col1, col2 = st.columns([1, 1])

                    with col1:
                        # 显示队徽描述
                        st.write("**队徽设计描述:**")
                        if auto_logo.get("logo_description"):
                            st.text_area("", auto_logo["logo_description"], height=200, key=f"auto_logo_desc_{auto_logo.get('team_name', 'unknown')}")
                        else:
                            st.info("队徽描述生成中...")

                    with col2:
                        st.write("**队徽信息:**")
                        st.write(f"**球队名称:** {auto_logo.get('team_name', 'N/A')}")
                        st.write(f"**触发方式:** {auto_logo.get('auto_trigger', 'N/A')}")

                        # 注意：这个版本没有图像生成功能，只有描述
                        st.info("💡 队徽描述已生成，可用于设计参考")

        except Exception as e:
            st.error(f"处理自动生成队徽时出错: {e}")

    def _process_basic_message(self, user_message: str, team_name: str) -> str:
        """处理基础消息（原有逻辑）"""
        current_team_stats = self.team_service.get_team_stats(team_name)
        messages = [
            {"role": "system", "content": self.ai_service.get_system_prompt(team_name, current_team_stats)},
            {"role": "user", "content": user_message}
        ]
        return self.ai_service.generate_response(messages)

    def _build_conversation_history(self, team_name: str) -> List[Dict[str, str]]:
        """构建对话历史"""
        current_team_stats = self.team_service.get_team_stats(team_name)

        # 系统提示词（包含函数调用指导）
        system_prompt = f"""你是一个专业的足球队信息收集助手。你的任务是帮助用户收集完整的球队和球员信息，用于生成报名表。

当前球队状态：
- 球队名称：{team_name}
- 球员总数：{current_team_stats['total_players']}人
- 已上传照片：{current_team_stats['players_with_photos']}人
- 完成度：{current_team_stats['completion_rate']:.1f}%

重要说明：
1. 当用户提供球队信息时，使用 extract_team_info 函数提取和保存信息
2. 当用户提供球员信息时，使用 extract_player_info 函数提取和保存信息
3. 主动引导用户补充缺失的重要信息
4. 验证数据的准确性和完整性
5. 一步步收集所需信息，不要一次性要求太多

必需的球队信息：
- 球队名称、联系人、联系电话
- 教练信息（姓名、电话）
- 比赛信息（名称、时间、地点、组别）
- 球衣颜色信息

请用友好、专业的语气与用户交流，主动使用函数调用来保存和处理数据。"""

        messages = [{"role": "system", "content": system_prompt}]

        # 添加历史对话
        if "ai_messages" in st.session_state:
            for msg in st.session_state.ai_messages:
                if msg["role"] != "system":
                    messages.append(msg)

        return messages

    def _handle_function_results(self, function_results: List[Dict[str, Any]], team_name: str):
        """处理函数调用结果"""
        for result in function_results:
            if result.get("success"):
                # 处理球队信息提取结果
                if "extracted_info" in result:
                    extracted_info = result["extracted_info"]

                    # 保存到session state
                    if "extracted_team_info" not in st.session_state:
                        st.session_state.extracted_team_info = {}

                    st.session_state.extracted_team_info.update(extracted_info)

                    # 尝试保存到实际的球队数据
                    self._save_extracted_team_info(extracted_info, team_name)

                # 处理球员信息提取结果
                if "players" in result:
                    players = result["players"]
                    self._save_extracted_player_info(players, team_name)

    def _save_extracted_team_info(self, extracted_info: Dict[str, Any], team_name: str):
        """保存提取的球队信息"""
        try:
            # 这里可以调用实际的保存逻辑
            # 目前先保存到session state
            st.session_state[f"team_info_{team_name}"] = extracted_info
            st.success("✅ 球队信息已保存")
        except Exception as e:
            st.error(f"❌ 保存球队信息失败: {e}")

    def _save_extracted_player_info(self, players: List[Dict[str, Any]], team_name: str):
        """保存提取的球员信息"""
        try:
            # 这里可以调用实际的保存逻辑
            # 目前先保存到session state
            if f"players_{team_name}" not in st.session_state:
                st.session_state[f"players_{team_name}"] = []

            st.session_state[f"players_{team_name}"].extend(players)
            st.success(f"✅ 已保存 {len(players)} 名球员信息")
        except Exception as e:
            st.error(f"❌ 保存球员信息失败: {e}")

    def show_enhanced_features_status(self):
        """显示增强功能状态"""
        if self.ai_service.has_enhanced_features():
            st.success("🚀 增强AI功能已启用（支持函数调用和结构化输出）")
        else:
            st.info("ℹ️ 使用基础AI功能")

    def show_extracted_data_summary(self, team_name: str):
        """显示提取的数据摘要"""
        team_info_key = f"team_info_{team_name}"
        players_key = f"players_{team_name}"

        if team_info_key in st.session_state or players_key in st.session_state:
            st.markdown("### 📊 AI提取的信息")

            # 显示球队信息
            if team_info_key in st.session_state:
                team_info = st.session_state[team_info_key]
                with st.expander("🏆 球队信息", expanded=True):
                    st.json(team_info)

            # 显示球员信息
            if players_key in st.session_state:
                players = st.session_state[players_key]
                with st.expander(f"👥 球员信息 ({len(players)}人)", expanded=False):
                    for i, player in enumerate(players, 1):
                        st.write(f"**球员 {i}:**")
                        st.json(player)

    def _render_word_generation_panel(self, team_name: str) -> None:
        """
        渲染Word生成面板

        Args:
            team_name: 球队名称
        """
        # 初始化Word生成器
        self._init_workflow_components()

        if self.word_generator is None:
            return

        # 获取当前球队和球员数据
        try:
            # 获取球队信息
            team_info = self.team_service.get_team_info(team_name)
            players_objects = self.player_service.get_players(team_name)

            # 构建team_data字典（使用默认值，因为当前系统中没有存储教练、领队信息）
            team_data = {
                'name': team_name,
                'leader': '',  # 可以从AI对话中提取或让用户填写
                'coach': '',   # 可以从AI对话中提取或让用户填写
                'doctor': ''   # 可以从AI对话中提取或让用户填写
            }

            # 将Player对象转换为字典格式
            players_data = []
            if players_objects:
                for player in players_objects:
                    if hasattr(player, 'to_dict'):
                        # 如果有to_dict方法，使用它
                        player_dict = player.to_dict()
                    else:
                        # 否则手动构建字典
                        player_dict = {
                            'name': getattr(player, 'name', ''),
                            'jersey_number': getattr(player, 'jersey_number', ''),
                            'photo': getattr(player, 'photo', '')
                        }
                    players_data.append(player_dict)

            if team_data and players_data:
                # 渲染Word生成面板
                self.word_generator.render_word_generation_panel(team_data, players_data)
            else:
                # 如果没有完整数据，显示简化的提示
                st.markdown("---")
                st.markdown("### 📄 Word报名表生成")
                st.info("💡 完善球队和球员信息后，即可生成专业的Word报名表")

        except Exception as e:
            st.error(f"❌ Word生成面板加载失败: {e}")
