#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
球队管理系统 - Streamlit版本
用于处理球员照片上传、信息管理等功能
"""

import streamlit as st
import os
import json
import uuid
import asyncio
from datetime import datetime
from PIL import Image
import pandas as pd
from pathlib import Path
from openai import OpenAI

# 配置页面
st.set_page_config(
    page_title="淄川五人制球队管理系统",
    page_icon="⚽",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 配置路径
DATA_FOLDER = 'data'
UPLOAD_FOLDER = 'uploads'
AI_EXPORT_FOLDER = 'ai_export'  # AI处理专用文件夹

# 确保必要的文件夹存在
for folder in [DATA_FOLDER, UPLOAD_FOLDER, AI_EXPORT_FOLDER]:
    if not os.path.exists(folder):
        os.makedirs(folder)

def get_safe_team_name(team_name):
    """获取安全的球队名称（用于文件名）"""
    return "".join(c for c in team_name if c.isalnum() or c in (' ', '-', '_')).strip() or "default"

def get_team_upload_folder(team_name):
    """获取球队专属的上传文件夹"""
    safe_name = get_safe_team_name(team_name)
    team_folder = os.path.join(UPLOAD_FOLDER, safe_name)
    if not os.path.exists(team_folder):
        os.makedirs(team_folder)
    return team_folder

def load_team_data(team_name="default"):
    """加载球队数据"""
    safe_name = get_safe_team_name(team_name)
    data_file = os.path.join(DATA_FOLDER, f'team_{safe_name}.json')
    
    try:
        if os.path.exists(data_file):
            with open(data_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            return {
                "players": [], 
                "team_info": {
                    "name": team_name, 
                    "created_at": datetime.now().isoformat()
                }
            }
    except Exception as e:
        st.error(f"加载数据失败: {e}")
        return {
            "players": [], 
            "team_info": {
                "name": team_name, 
                "created_at": datetime.now().isoformat()
            }
        }

def save_team_data(data, team_name="default"):
    """保存球队数据"""
    safe_name = get_safe_team_name(team_name)
    data_file = os.path.join(DATA_FOLDER, f'team_{safe_name}.json')
    
    try:
        with open(data_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        st.error(f"保存数据失败: {e}")
        return False

def get_teams_list():
    """获取所有球队列表"""
    teams = []
    if os.path.exists(DATA_FOLDER):
        for filename in os.listdir(DATA_FOLDER):
            if filename.startswith('team_') and filename.endswith('.json'):
                team_name = filename[5:-5]  # 移除 'team_' 前缀和 '.json' 后缀
                teams.append(team_name)

    # 确保默认球队在列表中
    if 'default' not in teams:
        teams.insert(0, 'default')

    return teams

def create_team(team_name):
    """创建新球队"""
    teams = get_teams_list()
    if team_name and team_name not in teams:
        # 创建新球队（通过保存空数据）
        empty_data = {
            "players": [],
            "team_info": {
                "name": team_name,
                "created_at": datetime.now().isoformat()
            }
        }
        if save_team_data(empty_data, team_name):
            st.session_state.current_team = team_name
            return True
    return False



def process_uploaded_image(image_file, save_path, max_size=(800, 800)):
    """处理上传的图片：压缩和优化"""
    try:
        # 打开图片
        img = Image.open(image_file)
        
        # 转换为RGB模式（如果需要）
        if img.mode in ('RGBA', 'P'):
            img = img.convert('RGB')
        
        # 计算新尺寸（保持宽高比）
        img.thumbnail(max_size, Image.Resampling.LANCZOS)
        
        # 保存优化后的图片
        img.save(save_path, 'JPEG', quality=85, optimize=True)
        
        return True
    except Exception as e:
        st.error(f"图片处理失败: {e}")
        return False

def add_player(team_name, name, jersey_number, photo_file=None):
    """添加新球员"""
    # 验证必填字段
    if not name or not jersey_number:
        st.error('姓名和球衣号码是必填项')
        return False
    
    # 检查球衣号码是否已存在
    data = load_team_data(team_name)
    for player in data['players']:
        if player['jersey_number'] == str(jersey_number):
            st.error(f'球衣号码 {jersey_number} 已被使用')
            return False
    
    # 处理上传的照片
    photo_filename = None
    if photo_file is not None:
        # 生成唯一文件名
        file_extension = photo_file.name.split('.')[-1].lower()
        photo_filename = f"{uuid.uuid4().hex}.{file_extension}"
        
        # 获取球队专属的上传文件夹
        team_upload_folder = get_team_upload_folder(team_name)
        file_path = os.path.join(team_upload_folder, photo_filename)
        
        # 处理并保存图片
        if not process_uploaded_image(photo_file, file_path):
            return False
    
    # 创建球员信息
    player = {
        'id': str(uuid.uuid4()),
        'name': name,
        'jersey_number': str(jersey_number),
        'photo': photo_filename,
        'created_at': datetime.now().isoformat(),
        'updated_at': datetime.now().isoformat()
    }
    
    # 保存到数据库
    data['players'].append(player)
    if save_team_data(data, team_name):
        # 自动导出到AI处理文件夹（后台进行）
        auto_export_for_ai(team_name)
        st.success(f'球员 {name} (#{jersey_number}) 添加成功！')
        return True
    else:
        st.error('保存数据失败')
        return False

def delete_player(team_name, player_id):
    """删除球员"""
    data = load_team_data(team_name)
    player = None
    player_index = -1
    
    # 查找球员
    for i, p in enumerate(data['players']):
        if p['id'] == player_id:
            player = p
            player_index = i
            break
    
    if not player:
        st.error('球员不存在')
        return False
    
    # 删除照片文件
    if player['photo']:
        team_upload_folder = get_team_upload_folder(team_name)
        photo_path = os.path.join(team_upload_folder, player['photo'])
        if os.path.exists(photo_path):
            os.remove(photo_path)
    
    # 从数据中删除
    data['players'].pop(player_index)
    
    if save_team_data(data, team_name):
        # 自动导出到AI处理文件夹（后台进行）
        auto_export_for_ai(team_name)
        st.success(f'球员 {player["name"]} 删除成功！')
        return True
    else:
        st.error('保存数据失败')
        return False

def create_team(team_name):
    """创建新球队"""
    teams = get_teams_list()

    if team_name and team_name not in teams:
        # 创建新球队（通过保存空数据）
        empty_data = {
            "players": [],
            "team_info": {
                "name": team_name,
                "created_at": datetime.now().isoformat()
            }
        }
        if save_team_data(empty_data, team_name):
            st.session_state.current_team = team_name
            return True
        else:
            st.error('创建球队失败')
            return False
    elif team_name in teams:
        st.error('球队名称已存在')
        return False
    else:
        st.error('请输入球队名称')
        return False

def auto_export_for_ai(team_name):
    """自动导出数据到AI处理文件夹"""
    data = load_team_data(team_name)

    if not data['players']:
        return None  # 没有球员数据，不需要导出

    # 为AI处理准备数据格式
    export_data = {
        'team_info': {
            'name': team_name,
            'display_name': '默认球队' if team_name == 'default' else team_name,
            'created_at': data.get('team_info', {}).get('created_at', ''),
            'total_players': len(data['players'])
        },
        'players': [],
        'export_time': datetime.now().isoformat(),
        'ai_processing': {
            'status': 'ready',
            'next_steps': ['photo_processing', 'form_generation'],
            'photo_folder': f"uploads/{get_safe_team_name(team_name)}",
            'export_folder': AI_EXPORT_FOLDER
        }
    }

    for player in data['players']:
        # 获取照片的绝对路径
        photo_abs_path = None
        photo_relative_path = None
        if player['photo']:
            photo_relative_path = f"uploads/{get_safe_team_name(team_name)}/{player['photo']}"
            photo_abs_path = os.path.abspath(photo_relative_path)

        player_data = {
            'id': player['id'],
            'name': player['name'],
            'jersey_number': player['jersey_number'],
            'photo_info': {
                'filename': player['photo'],
                'relative_path': photo_relative_path,
                'absolute_path': photo_abs_path,
                'exists': os.path.exists(photo_abs_path) if photo_abs_path else False
            },
            'created_at': player.get('created_at', ''),
            'ai_tags': {
                'person_id': f"{team_name}_{player['jersey_number']}_{player['name']}",
                'display_name': f"{player['name']} (#{player['jersey_number']})",
                'team': team_name
            }
        }
        export_data['players'].append(player_data)

    # 保存到AI处理文件夹
    safe_team_name = get_safe_team_name(team_name)
    ai_export_file = os.path.join(AI_EXPORT_FOLDER, f'team_{safe_team_name}_ai_ready.json')

    try:
        with open(ai_export_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        return ai_export_file
    except Exception as e:
        st.error(f"自动导出失败: {e}")
        return None

def export_team_data(team_name):
    """导出球队数据（用于手动下载）"""
    data = load_team_data(team_name)

    # 为AI处理准备数据格式
    export_data = {
        'team_info': data.get('team_info', {}),
        'players': [],
        'export_time': datetime.now().isoformat(),
        'total_players': len(data['players'])
    }

    for player in data['players']:
        player_data = {
            'id': player['id'],
            'name': player['name'],
            'jersey_number': player['jersey_number'],
            'photo_path': f"uploads/{get_safe_team_name(team_name)}/{player['photo']}" if player['photo'] else None,
            'photo_file': player['photo']
        }
        export_data['players'].append(player_data)

    return export_data

# 初始化session state
if 'current_team' not in st.session_state:
    st.session_state.current_team = 'default'

if 'show_add_form' not in st.session_state:
    st.session_state.show_add_form = False

# 主应用界面
def main():
    st.title("⚽ 淄川五人制球队管理系统")
    st.markdown("管理球员信息，上传照片，为AI处理做准备")
    
    # 侧边栏 - 球队管理
    with st.sidebar:
        st.header("球队管理")
        
        # 获取球队列表
        teams = get_teams_list()
        
        # 球队选择
        team_display_names = []
        for team in teams:
            if team == 'default':
                team_display_names.append('默认球队')
            else:
                team_display_names.append(team)

        selected_display = st.selectbox(
            "选择球队",
            team_display_names,
            index=team_display_names.index('默认球队' if st.session_state.current_team == 'default' else st.session_state.current_team)
        )

        # 更新当前球队
        if selected_display == '默认球队':
            st.session_state.current_team = 'default'
        else:
            st.session_state.current_team = selected_display

        # 创建新球队
        st.subheader("创建新球队")
        new_team_name = st.text_input("球队名称", placeholder="请输入球队名称")
        if st.button("创建球队"):
            if new_team_name and new_team_name not in teams:
                # 创建新球队（通过保存空数据）
                empty_data = {
                    "players": [],
                    "team_info": {
                        "name": new_team_name,
                        "created_at": datetime.now().isoformat()
                    }
                }
                if save_team_data(empty_data, new_team_name):
                    st.success(f'球队 "{new_team_name}" 创建成功！')
                    st.session_state.current_team = new_team_name
                    st.rerun()
                else:
                    st.error('创建球队失败')
            elif new_team_name in teams:
                st.error('球队名称已存在')
            else:
                st.error('请输入球队名称')

        # 照片处理功能
        st.subheader("🎨 照片处理")
        st.markdown("*独立的AI照片处理功能*")
        if st.button("🎨 AI照片处理", use_container_width=True):
            st.session_state.batch_mode = 'photo_process'
            st.session_state.show_add_form = False
            st.rerun()
        st.markdown("*支持换装、背景去除、添加白底*")

        # 导出数据功能
        st.subheader("📊 系统功能")
        st.markdown("*管理员功能*")
        if st.button("📥 导出数据", use_container_width=True, help="管理员功能：导出球队数据供后台处理"):
            export_data = export_team_data(current_team)
            st.download_button(
                label="下载JSON文件",
                data=json.dumps(export_data, ensure_ascii=False, indent=2),
                file_name=f"team_{get_safe_team_name(current_team)}_{datetime.now().strftime('%Y%m%d')}.json",
                mime="application/json"
            )
        st.markdown("*用于后台数据处理和报名表生成*")
    
    # 主内容区域
    current_team = st.session_state.current_team
    team_display_name = '默认球队' if current_team == 'default' else current_team

    st.subheader(f"当前球队: {team_display_name}")

    # AI信息收集助手
    st.markdown("### 🤖 AI球队信息收集助手")

    # 设置OpenAI客户端
    client = OpenAI(api_key=st.secrets["OPENAI_API_KEY"])

    # 设置默认模型
    if "openai_model" not in st.session_state:
        st.session_state["openai_model"] = "gpt-4o-mini"

    # 初始化对话状态
    if "ai_messages" not in st.session_state:
        # 系统prompt，专门用于收集球队信息
        system_prompt = f"""你是一个专业的球队信息收集助手，正在帮助用户收集球队「{team_display_name}」的详细信息，用于生成报名表。

你的任务是按顺序收集以下信息：
1. 比赛名称（例如：2024年淄川市五人制足球联赛）
2. 比赛时间（例如：2024年9月15日）
3. 比赛地点（例如：淄川体育中心）
4. 教练姓名
5. 教练联系电话
6. 领队姓名（如果没有可以说"无"）
7. 领队联系电话（如果没有领队则跳过）
8. 参赛组别（例如：成年男子组、青年组等）

请用友好、专业的语气与用户对话，一次只询问一个信息，确认收到后再询问下一个。
当所有信息收集完成后，请提供一个完整的总结。

现在开始与用户对话，询问第一个信息：比赛名称。"""

        st.session_state.ai_messages = [
            {"role": "system", "content": system_prompt},
            {"role": "assistant", "content": f"您好！我是AI助手，将帮助您收集球队「{team_display_name}」的详细信息，用于生成报名表。\n\n请告诉我您需要填写哪项比赛的报名表？（请提供比赛的完整名称）"}
        ]

    # 创建AI对话容器
    chat_container = st.container()
    with chat_container:
        # 显示对话历史（跳过系统消息）
        for message in st.session_state.ai_messages:
            if message["role"] != "system":
                with st.chat_message(message["role"]):
                    st.markdown(message["content"])

    # 在AI对话区域正下方放置输入框
    with st.container():
        # 用户输入
        if prompt := st.chat_input("请输入您的回复..."):
            # 添加用户消息
            st.session_state.ai_messages.append({"role": "user", "content": prompt})

            # 显示用户消息
            with chat_container:
                with st.chat_message("user"):
                    st.markdown(prompt)

            # 生成AI回复
            with chat_container:
                with st.chat_message("assistant"):
                    stream = client.chat.completions.create(
                        model=st.session_state["openai_model"],
                        messages=[
                            {"role": m["role"], "content": m["content"]}
                            for m in st.session_state.ai_messages
                        ],
                        stream=True,
                    )
                    response = st.write_stream(stream)

            # 添加AI回复到历史
            st.session_state.ai_messages.append({"role": "assistant", "content": response})

            st.rerun()

    st.markdown("---")

    # 加载当前球队数据
    team_data = load_team_data(current_team)
    players = team_data['players']
    
    # 初始化session state
    if 'batch_mode' not in st.session_state:
        st.session_state.batch_mode = 'normal'  # normal, upload, edit
    if 'batch_photos' not in st.session_state:
        st.session_state.batch_photos = []
    if 'show_add_form' not in st.session_state:
        st.session_state.show_add_form = False

    # 根据球队状态显示不同的界面
    if not players:
        # 新球队球员添加引导界面
        st.markdown("---")
        st.markdown(f"### ⚽ 为球队「{current_team}」添加球员")
        st.info("👋 球队已创建成功！现在开始为您的球队添加球员吧：")

        # 步骤指引
        col1, col2 = st.columns([1, 2])

        with col1:
            st.markdown("#### 📋 添加球员步骤")
            st.markdown("""
            **第1步：选择添加方式**
            - 推荐使用"批量添加"上传所有球员照片
            - 或使用"单个添加"逐个添加球员

            **第2步：填写球员信息**
            - 为每张照片标注姓名和号码
            - 设置AI处理方案

            **第3步：完成建档**
            - 系统自动生成球员档案
            - 准备AI处理和报名表生成
            """)

        with col2:
            st.markdown("#### 🎯 开始添加球员")

            # 大按钮引导
            if st.button("🚀 开始批量添加球员", type="primary", use_container_width=True):
                st.session_state.batch_mode = 'upload'
                st.session_state.batch_photos = []
                st.session_state.show_add_form = False
                st.rerun()

            st.markdown("*推荐：一次性上传所有球员照片，快速建立完整档案*")

            st.markdown("---")

            # 单个添加选项
            if st.button("➕ 单个添加球员", use_container_width=True):
                st.session_state.show_add_form = True
                st.session_state.batch_mode = 'normal'
                st.rerun()

            st.markdown("*适合：只添加少数球员或补充球员信息*")

            st.markdown("---")

            # 提示信息
            st.markdown("💡 **提示**：如需创建其他球队，请使用左侧边栏的创建球队功能")

    else:
        # 已有球员的管理界面
        st.markdown("---")

        # 球员统计
        col1, col2, col3 = st.columns([2, 2, 2])

        with col1:
            st.metric("👥 球员总数", len(players))

        with col2:
            # 计算有照片的球员数量
            players_with_photos = len([p for p in players if p.get('photo')])
            st.metric("📸 已上传照片", f"{players_with_photos}/{len(players)}")

        with col3:
            # 显示球队状态
            if players_with_photos == len(players):
                st.success("✅ 球队档案完整")
            else:
                st.warning(f"⚠️ 还有 {len(players) - players_with_photos} 人未上传照片")

        # 主要操作按钮
        col_create, col_title = st.columns([1, 2])

        with col_create:
            st.markdown("### 📝 创建新球队")

        with col_title:
            st.markdown("### 🎯 球员管理操作")

        col1, col2, col3, col4 = st.columns([1.5, 0.5, 1, 1])

        with col1:
            new_team_name_main = st.text_input("球队名称", placeholder="请输入球队名称", key="main_team_name", label_visibility="collapsed")

        with col2:
            if st.button("创建", use_container_width=True, key="main_create_team"):
                if new_team_name_main.strip():
                    create_team(new_team_name_main.strip())
                    st.success(f"球队 '{new_team_name_main.strip()}' 创建成功！")
                    st.rerun()
                else:
                    st.error("请输入球队名称")

        with col3:
            if st.button("➕ 添加球员", use_container_width=True):
                st.session_state.show_add_form = True
                st.session_state.batch_mode = 'normal'
                st.rerun()

        with col4:
            if st.button("📤 批量添加", use_container_width=True):
                st.session_state.batch_mode = 'upload'
                st.session_state.batch_photos = []
                st.session_state.show_add_form = False
                st.rerun()



    # 添加球员表单
    if st.session_state.show_add_form:
        st.subheader("添加新球员")

        with st.form("add_player_form"):
            col1, col2 = st.columns(2)

            with col1:
                player_name = st.text_input("球员姓名 *", placeholder="请输入球员姓名")
                jersey_number = st.number_input("球衣号码 *", min_value=1, max_value=99, value=1)

            with col2:
                uploaded_photo = st.file_uploader(
                    "球员照片",
                    type=['png', 'jpg', 'jpeg', 'gif', 'bmp'],
                    help="支持 PNG, JPG, JPEG, GIF, BMP 格式"
                )

                # 添加中文提示和英文说明
                if uploaded_photo is None:
                    st.info("💡 提示：点击上方区域选择球员照片")
                    st.markdown("""
                    <div style="background-color: #f0f2f6; padding: 8px; border-radius: 5px; margin-top: 5px;">
                        <small>
                        📝 <strong>英文界面说明：</strong><br>
                        • "Drag and drop file here" = 拖拽文件到此处<br>
                        • "Browse files" = 浏览文件
                        </small>
                    </div>
                    """, unsafe_allow_html=True)

                # 显示照片预览
                if uploaded_photo is not None:
                    st.image(uploaded_photo, caption="照片预览", width=200)

            # 表单按钮
            col1, col2 = st.columns(2)
            with col1:
                submitted = st.form_submit_button("保存球员", type="primary")
            with col2:
                cancelled = st.form_submit_button("取消")

            if submitted:
                if add_player(current_team, player_name, jersey_number, uploaded_photo):
                    st.session_state.show_add_form = False
                    st.rerun()

            if cancelled:
                st.session_state.show_add_form = False
                st.rerun()

    # 批量上传界面
    elif st.session_state.batch_mode == 'upload':
        st.subheader("📤 批量上传球员照片")
        st.markdown("一次性上传所有球员照片，然后为每张照片添加球员信息")

        uploaded_files = st.file_uploader(
            "选择多张球员照片",
            type=['png', 'jpg', 'jpeg', 'gif', 'bmp'],
            accept_multiple_files=True,
            help="可以同时选择多张照片，支持 PNG, JPG, JPEG, GIF, BMP 格式"
        )

        # 添加中文提示和英文说明
        if not uploaded_files:
            st.info("💡 提示：点击上方区域或拖拽照片文件到此处进行上传")
            st.markdown("""
            <div style="background-color: #f0f2f6; padding: 10px; border-radius: 5px; margin-top: 10px;">
                <small>
                📝 <strong>英文界面说明：</strong><br>
                • "Drag and drop files here" = 拖拽多个文件到此处<br>
                • "Browse files" = 浏览文件<br>
                • "Choose File" = 选择文件<br>
                • "Limit 200MB per file" = 单个文件大小限制200MB
                </small>
            </div>
            """, unsafe_allow_html=True)

        if uploaded_files:
            st.success(f"已选择 {len(uploaded_files)} 张照片")

            # 显示照片预览
            st.markdown("### 照片预览")
            # 使用更少的列数，给每张照片更多空间
            cols_per_row = min(len(uploaded_files), 3)  # 最多3列
            cols = st.columns(cols_per_row)
            for i, uploaded_file in enumerate(uploaded_files):
                with cols[i % cols_per_row]:
                    st.image(uploaded_file, caption=f"照片 {i+1}", width=200)

            col1, col2 = st.columns(2)
            with col1:
                if st.button("✅ 确认上传，开始添加球员信息", type="primary"):
                    st.session_state.batch_photos = uploaded_files
                    st.session_state.batch_mode = 'edit'
                    st.rerun()

            with col2:
                if st.button("❌ 取消批量上传"):
                    st.session_state.batch_mode = 'normal'
                    st.session_state.batch_photos = []
                    st.rerun()
        else:
            st.info("请选择要上传的球员照片")

            if st.button("❌ 取消批量上传"):
                st.session_state.batch_mode = 'normal'
                st.rerun()

    # 批量编辑界面
    elif st.session_state.batch_mode == 'edit':
        st.subheader("✏️ 球员信息标注 + AI处理配置")
        st.markdown("为每张照片填写球员信息，并设置个性化AI处理方案，一次性完成所有配置")

        # 处理选项定义（复用之前的定义）
        PROCESS_OPTIONS = {
            "不处理": {
                "label": "🚫 不处理",
                "description": "跳过此球员，不进行任何处理",
                "fashion": False,
                "background": False,
                "white": False
            },
            "仅白底": {
                "label": "⚪ 仅白底",
                "description": "只添加白色背景",
                "fashion": False,
                "background": False,
                "white": True
            },
            "仅背景去除": {
                "label": "🖼️ 仅背景去除",
                "description": "只移除照片背景",
                "fashion": False,
                "background": True,
                "white": False
            },
            "仅换装": {
                "label": "🔄 仅换装",
                "description": "只进行AI换装（需要模板图）",
                "fashion": True,
                "background": False,
                "white": False
            },
            "背景去除+白底": {
                "label": "🖼️⚪ 背景去除+白底",
                "description": "去除背景后添加白底",
                "fashion": False,
                "background": True,
                "white": True
            },
            "换装+白底": {
                "label": "🔄⚪ 换装+白底",
                "description": "换装后添加白底",
                "fashion": True,
                "background": False,
                "white": True
            },
            "换装+背景去除": {
                "label": "🔄🖼️ 换装+背景去除",
                "description": "换装后去除背景",
                "fashion": True,
                "background": True,
                "white": False
            },
            "全套处理": {
                "label": "🔄🖼️⚪ 全套处理",
                "description": "换装+背景去除+白底",
                "fashion": True,
                "background": True,
                "white": True
            }
        }

        if st.session_state.batch_photos:
            # 模板图上传区域
            st.markdown("### 🎨 模板图上传（可选）")
            st.markdown("*如果需要为球员进行换装处理，请上传模板图*")

            clothes_image = st.file_uploader(
                "选择模板图片（换装时需要）",
                type=['png', 'jpg', 'jpeg'],
                help="用于AI换装的模板图片，系统会将球员照片中的服装替换为此模板样式"
            )

            if clothes_image:
                col1, col2 = st.columns([1, 2])
                with col1:
                    st.image(clothes_image, caption="模板图预览", width=200)
                with col2:
                    st.info("✅ 模板图已选择")
                    st.markdown("**模板图说明：**")
                    st.markdown("- 🎽 **球衣模板**：用于统一球员球衣样式")
                    st.markdown("- 👕 **服装模板**：用于更换球员服装风格")
                    st.markdown("- 🎨 **设计模板**：用于展示不同设计效果")

            # 批量设置区域
            st.markdown("### ⚡ 批量设置处理方案")
            st.markdown("*快速为所有球员设置相同的处理方案，然后可以单独调整*")

            col1, col2, col3, col4 = st.columns(4)

            with col1:
                if st.button("🔄🖼️⚪ 全部设为全套处理", key="batch_full"):
                    for i in range(len(st.session_state.batch_photos)):
                        st.session_state[f"batch_process_{i}"] = "全套处理"
                    st.rerun()

            with col2:
                if st.button("🖼️⚪ 全部设为背景去除+白底", key="batch_bg_white"):
                    for i in range(len(st.session_state.batch_photos)):
                        st.session_state[f"batch_process_{i}"] = "背景去除+白底"
                    st.rerun()

            with col3:
                if st.button("⚪ 全部设为仅白底", key="batch_white"):
                    for i in range(len(st.session_state.batch_photos)):
                        st.session_state[f"batch_process_{i}"] = "仅白底"
                    st.rerun()

            with col4:
                if st.button("🚫 全部设为不处理", key="batch_none"):
                    for i in range(len(st.session_state.batch_photos)):
                        st.session_state[f"batch_process_{i}"] = "不处理"
                    st.rerun()

            with st.form("batch_edit_form"):
                # 存储所有球员信息
                player_data = []
                needs_template = False

                # 为每张照片创建输入区域
                for i, photo in enumerate(st.session_state.batch_photos):
                    st.markdown(f"#### 球员 {i+1}")

                    # 使用三列布局：照片、基本信息、处理配置
                    col1, col2, col3 = st.columns([2, 2, 2])

                    with col1:
                        # 照片显示
                        st.image(photo, width=250, caption=f"球员照片 {i+1}")

                    with col2:
                        # 基本信息输入
                        st.markdown("**基本信息**")
                        name = st.text_input(
                            f"球员姓名 *",
                            key=f"name_{i}",
                            placeholder="请输入球员姓名"
                        )

                        # 获取已使用的号码
                        used_numbers = [p['jersey_number'] for p in players]
                        jersey_number = st.number_input(
                            f"球衣号码 *",
                            min_value=1,
                            max_value=99,
                            value=i+1,
                            key=f"number_{i}",
                            help=f"已使用号码: {', '.join(map(str, used_numbers)) if used_numbers else '无'}"
                        )

                    with col3:
                        # 处理方案配置
                        st.markdown("**AI处理方案**")

                        # 初始化默认处理选项
                        if f"batch_process_{i}" not in st.session_state:
                            st.session_state[f"batch_process_{i}"] = "全套处理"

                        current_option = st.session_state.get(f"batch_process_{i}", "全套处理")

                        selected_option = st.selectbox(
                            "处理方案",
                            options=list(PROCESS_OPTIONS.keys()),
                            index=list(PROCESS_OPTIONS.keys()).index(current_option),
                            key=f"batch_process_{i}",
                            format_func=lambda x: PROCESS_OPTIONS[x]["label"]
                        )

                        # 显示选项说明
                        option_info = PROCESS_OPTIONS[selected_option]
                        st.markdown(f"**说明：** {option_info['description']}")

                        # 检查是否需要模板图
                        if option_info["fashion"]:
                            needs_template = True

                    player_data.append({
                        'name': name,
                        'jersey_number': jersey_number,
                        'photo': photo,
                        'process_option': selected_option
                    })

                    st.divider()

                # 处理统计
                st.markdown("### 📊 处理统计")

                # 统计各种处理类型
                process_stats = {}
                for data in player_data:
                    option = data['process_option']
                    if option not in process_stats:
                        process_stats[option] = 0
                    process_stats[option] += 1

                st.markdown("**处理方案统计：**")
                for option, count in process_stats.items():
                    if count > 0:
                        option_info = PROCESS_OPTIONS[option]
                        st.markdown(f"- {option_info['label']}: {count}人")

                # 验证和保存
                st.markdown("### 💾 保存配置")

                # 验证条件
                validation_errors = []

                # 检查基本信息
                for i, data in enumerate(player_data):
                    if not data['name'].strip():
                        validation_errors.append(f"球员 {i+1} 缺少姓名")

                # 检查号码重复
                jersey_numbers = [data['jersey_number'] for data in player_data if data['name'].strip()]
                if len(jersey_numbers) != len(set(jersey_numbers)):
                    validation_errors.append("存在重复的球衣号码")

                # 检查模板图
                if needs_template and not clothes_image:
                    validation_errors.append("有球员需要换装处理，请上传模板图")

                # 显示验证结果
                if validation_errors:
                    for error in validation_errors:
                        st.error(f"❌ {error}")
                else:
                    st.success("✅ 所有配置验证通过")

                # 批量保存按钮
                col1, col2, col3 = st.columns(3)
                with col1:
                    save_only = st.form_submit_button("💾 仅保存球员信息", disabled=bool(validation_errors))

                with col2:
                    save_and_process = st.form_submit_button("🚀 保存并开始AI处理", type="primary", disabled=bool(validation_errors))

                with col3:
                    cancel = st.form_submit_button("❌ 取消")

                if save_only or save_and_process:
                    success_count = 0
                    error_messages = []
                    saved_players = []

                    # 保存模板图片（如果有）
                    clothes_image_path = None
                    if needs_template and clothes_image:
                        clothes_dir = os.path.join("processed_photos", "templates")
                        os.makedirs(clothes_dir, exist_ok=True)
                        clothes_image_path = os.path.join(clothes_dir, f"batch_template_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")

                        with open(clothes_image_path, "wb") as f:
                            f.write(clothes_image.getvalue())

                    # 保存球员信息
                    for i, data in enumerate(player_data):
                        if data['name'].strip():
                            try:
                                if add_player(current_team, data['name'], data['jersey_number'], data['photo']):
                                    success_count += 1
                                    # 保存处理配置到session state
                                    player_id = f"{data['name']}_{data['jersey_number']}"
                                    saved_players.append({
                                        'name': data['name'],
                                        'jersey_number': data['jersey_number'],
                                        'process_option': data['process_option'],
                                        'player_id': player_id
                                    })
                                else:
                                    error_messages.append(f"球员{i+1}: 保存失败")
                            except Exception as e:
                                error_messages.append(f"球员{i+1}: {str(e)}")
                        else:
                            error_messages.append(f"球员{i+1}: 请输入球员姓名")

                    if success_count > 0:
                        st.success(f"✅ 成功添加 {success_count} 名球员！")

                        # 如果选择了保存并处理
                        if save_and_process:
                            st.info("🚀 准备开始AI处理...")

                            # 生成处理配置
                            processing_config = {
                                "team": current_team,
                                "template_image": clothes_image_path,
                                "processing_players": []
                            }

                            for player_data in saved_players:
                                option_info = PROCESS_OPTIONS[player_data['process_option']]
                                processing_config["processing_players"].append({
                                    "player_info": {
                                        "name": player_data["name"],
                                        "jersey_number": player_data["jersey_number"]
                                    },
                                    "process_option": player_data['process_option'],
                                    "process_steps": {
                                        "fashion_tryon": option_info["fashion"],
                                        "remove_background": option_info["background"],
                                        "add_white_background": option_info["white"]
                                    }
                                })

                            # 显示处理配置
                            with st.expander("查看AI处理配置", expanded=True):
                                st.json(processing_config)

                            st.warning("⚠️ AI照片处理功能需要异步环境支持")
                            st.info("💡 建议使用独立的处理脚本进行批量AI处理")

                    if error_messages:
                        for msg in error_messages:
                            st.error(msg)

                    if success_count == len([d for d in player_data if d['name'].strip()]):
                        # 清理session state
                        for i in range(len(st.session_state.batch_photos)):
                            if f"batch_process_{i}" in st.session_state:
                                del st.session_state[f"batch_process_{i}"]

                        st.session_state.batch_mode = 'normal'
                        st.session_state.batch_photos = []
                        st.rerun()

                if cancel:
                    # 清理session state
                    for i in range(len(st.session_state.batch_photos)):
                        if f"batch_process_{i}" in st.session_state:
                            del st.session_state[f"batch_process_{i}"]

                    st.session_state.batch_mode = 'normal'
                    st.session_state.batch_photos = []
                    st.rerun()
        else:
            st.error("没有照片数据，请重新上传")
            if st.button("返回上传"):
                st.session_state.batch_mode = 'upload'
                st.rerun()

    # 照片处理界面
    elif st.session_state.batch_mode == 'photo_process':
        st.subheader("🎨 AI照片处理")
        st.markdown("为球员照片进行个性化AI处理：每个球员可选择不同的处理方案")

        if not players:
            st.warning("当前球队没有球员，请先添加球员")
            if st.button("❌ 返回"):
                st.session_state.batch_mode = 'normal'
                st.rerun()
        else:
            # 处理选项定义
            PROCESS_OPTIONS = {
                "不处理": {
                    "label": "🚫 不处理",
                    "description": "跳过此球员，不进行任何处理",
                    "fashion": False,
                    "background": False,
                    "white": False
                },
                "仅白底": {
                    "label": "⚪ 仅白底",
                    "description": "只添加白色背景",
                    "fashion": False,
                    "background": False,
                    "white": True
                },
                "仅背景去除": {
                    "label": "🖼️ 仅背景去除",
                    "description": "只移除照片背景",
                    "fashion": False,
                    "background": True,
                    "white": False
                },
                "仅换装": {
                    "label": "🔄 仅换装",
                    "description": "只进行AI换装（需要模板图）",
                    "fashion": True,
                    "background": False,
                    "white": False
                },
                "背景去除+白底": {
                    "label": "🖼️⚪ 背景去除+白底",
                    "description": "去除背景后添加白底",
                    "fashion": False,
                    "background": True,
                    "white": True
                },
                "换装+白底": {
                    "label": "🔄⚪ 换装+白底",
                    "description": "换装后添加白底",
                    "fashion": True,
                    "background": False,
                    "white": True
                },
                "换装+背景去除": {
                    "label": "🔄🖼️ 换装+背景去除",
                    "description": "换装后去除背景",
                    "fashion": True,
                    "background": True,
                    "white": False
                },
                "全套处理": {
                    "label": "🔄🖼️⚪ 全套处理",
                    "description": "换装+背景去除+白底",
                    "fashion": True,
                    "background": True,
                    "white": True
                }
            }

            # 模板图上传（如果有换装需求）
            st.markdown("### 上传模板图")
            st.markdown("*如果需要换装处理，请上传模板图。模板图是用于AI换装的参考图片，可以是球衣、队服或其他服装样式图片*")

            clothes_image = st.file_uploader(
                "选择模板图片（换装时需要）",
                type=['png', 'jpg', 'jpeg'],
                help="用于AI换装的模板图片，系统会将球员照片中的服装替换为此模板样式"
            )

            # 添加英文界面说明
            st.markdown("""
            <div style="background-color: #f0f2f6; padding: 10px; border-radius: 5px; margin-top: 10px;">
                <small>
                📝 <strong>界面说明：</strong><br>
                • "Drag and drop file here" = 拖拽文件到此处<br>
                • "Browse files" = 浏览文件<br>
                • "Choose File" = 选择文件<br>
                • "Limit 200MB per file" = 文件大小限制200MB
                </small>
            </div>
            """, unsafe_allow_html=True)

            if clothes_image:
                col1, col2 = st.columns([1, 2])
                with col1:
                    st.image(clothes_image, caption="模板图预览", width=200)
                with col2:
                    st.info("✅ 模板图已选择")
                    st.markdown("**模板图说明：**")
                    st.markdown("- 🎽 **球衣模板**：用于统一球员球衣样式")
                    st.markdown("- 👕 **服装模板**：用于更换球员服装风格")
                    st.markdown("- 🎨 **设计模板**：用于展示不同设计效果")

            # 批量设置
            st.markdown("### 批量设置")
            st.markdown("*快速为所有球员设置相同的处理方案，然后可以单独调整特殊情况*")

            col1, col2, col3, col4 = st.columns(4)

            with col1:
                if st.button("🔄🖼️⚪ 全部设为全套处理"):
                    for i, player in enumerate(players):
                        st.session_state[f"process_option_{player['id']}"] = "全套处理"
                    st.rerun()

            with col2:
                if st.button("🖼️⚪ 全部设为背景去除+白底"):
                    for i, player in enumerate(players):
                        st.session_state[f"process_option_{player['id']}"] = "背景去除+白底"
                    st.rerun()

            with col3:
                if st.button("⚪ 全部设为仅白底"):
                    for i, player in enumerate(players):
                        st.session_state[f"process_option_{player['id']}"] = "仅白底"
                    st.rerun()

            with col4:
                if st.button("🚫 全部设为不处理"):
                    for i, player in enumerate(players):
                        st.session_state[f"process_option_{player['id']}"] = "不处理"
                    st.rerun()

            # 个性化球员处理配置
            st.markdown("### 个性化处理配置")
            st.markdown("*为每个球员选择合适的处理方案，默认为全套处理*")

            # 初始化球员处理选项（默认为全套处理）
            for player in players:
                if f"process_option_{player['id']}" not in st.session_state:
                    st.session_state[f"process_option_{player['id']}"] = "全套处理"

            # 使用网格布局显示球员
            cols_per_row = 2  # 每行2个球员，给更多空间显示选项
            for i in range(0, len(players), cols_per_row):
                cols = st.columns(cols_per_row)
                for j, player in enumerate(players[i:i+cols_per_row]):
                    if j < len(cols):
                        with cols[j]:
                            # 球员信息容器
                            with st.container():
                                st.markdown(f"#### {player['name']} (#{player['jersey_number']})")

                                # 显示球员照片
                                if player['photo']:
                                    photo_path = os.path.join(UPLOAD_FOLDER, get_safe_team_name(current_team), player['photo'])
                                    if os.path.exists(photo_path):
                                        st.image(photo_path, width=200)
                                    else:
                                        st.error("照片不存在")
                                else:
                                    st.info("无照片")

                                # 处理选项选择
                                current_option = st.session_state.get(f"process_option_{player['id']}", "全套处理")

                                selected_option = st.selectbox(
                                    "处理方案",
                                    options=list(PROCESS_OPTIONS.keys()),
                                    index=list(PROCESS_OPTIONS.keys()).index(current_option),
                                    key=f"process_option_{player['id']}",
                                    format_func=lambda x: PROCESS_OPTIONS[x]["label"]
                                )

                                # 显示选项说明
                                option_info = PROCESS_OPTIONS[selected_option]
                                st.markdown(f"**说明：** {option_info['description']}")

                                st.markdown("---")

            # 处理统计
            st.markdown("### 处理统计")

            # 统计各种处理类型的球员数量
            process_stats = {}
            needs_template = False

            for player in players:
                option_key = st.session_state.get(f"process_option_{player['id']}", "全套处理")
                option_info = PROCESS_OPTIONS[option_key]

                if option_key not in process_stats:
                    process_stats[option_key] = {"count": 0}
                process_stats[option_key]["count"] += 1

                # 检查是否需要模板图
                if option_info["fashion"]:
                    needs_template = True

            # 显示统计信息
            st.markdown("**处理方案统计：**")
            for option, stats in process_stats.items():
                if stats["count"] > 0:
                    option_info = PROCESS_OPTIONS[option]
                    st.markdown(f"- {option_info['label']}: {stats['count']}人")

            # 处理按钮和验证
            st.markdown("### 开始处理")

            # 验证条件
            processing_players = [p for p in players if st.session_state.get(f"process_option_{p['id']}", "全套处理") != "不处理"]

            if not processing_players:
                st.warning("没有球员需要处理，请至少为一名球员选择处理方案")
            elif needs_template and not clothes_image:
                st.warning("有球员需要换装处理，请上传模板图")
            else:
                col1, col2 = st.columns(2)

                with col1:
                    if st.button("🚀 开始AI处理", type="primary"):
                        # 保存模板图片（如果有）
                        clothes_image_path = None
                        if needs_template and clothes_image:
                            clothes_dir = os.path.join("processed_photos", "templates")
                            os.makedirs(clothes_dir, exist_ok=True)
                            clothes_image_path = os.path.join(clothes_dir, f"template_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")

                            with open(clothes_image_path, "wb") as f:
                                f.write(clothes_image.getvalue())

                        # 显示处理信息
                        st.info(f"将对 {len(processing_players)} 名球员进行个性化处理")

                        # 显示处理进度
                        progress_bar = st.progress(0)
                        status_text = st.empty()

                        # 开始处理
                        try:
                            status_text.text("🚀 正在启动AI照片处理...")

                            # 生成详细的处理配置
                            processing_config = {
                                "team": current_team,
                                "template_image": clothes_image_path if clothes_image_path else None,
                                "processing_players": []
                            }

                            for player in processing_players:
                                option_key = st.session_state.get(f"process_option_{player['id']}", "全套处理")
                                option_info = PROCESS_OPTIONS[option_key]

                                processing_config["processing_players"].append({
                                    "player_info": {
                                        "name": player["name"],
                                        "id": player["id"],
                                        "jersey_number": player["jersey_number"]
                                    },
                                    "process_option": option_key,
                                    "process_steps": {
                                        "fashion_tryon": option_info["fashion"],
                                        "remove_background": option_info["background"],
                                        "add_white_background": option_info["white"]
                                    }
                                })

                            # 由于Streamlit的限制，我们需要使用同步方式
                            st.warning("⚠️ AI照片处理功能需要异步环境支持")
                            st.info("💡 建议：")
                            st.markdown("""
                            1. **本地处理**：可以将配置导出，使用独立的处理脚本
                            2. **后台服务**：部署独立的照片处理服务
                            3. **队列系统**：使用任务队列进行异步处理
                            """)

                            # 显示详细的处理配置
                            with st.expander("查看详细处理配置", expanded=False):
                                st.json(processing_config)

                            progress_bar.progress(100)
                            status_text.text("✅ 个性化处理配置已生成")

                        except Exception as e:
                            st.error(f"处理失败: {e}")
                            status_text.text("❌ 处理失败")

                with col2:
                    if st.button("❌ 取消处理"):
                        st.session_state.batch_mode = 'normal'
                        st.rerun()

            # 处理历史记录
            st.markdown("### 处理历史")
            st.info("📝 处理历史记录功能开发中...")

    # 显示球员列表
    if players:
        st.subheader("球员名单")

        # 创建球员卡片网格
        cols_per_row = 3
        for i in range(0, len(players), cols_per_row):
            cols = st.columns(cols_per_row)

            for j, col in enumerate(cols):
                if i + j < len(players):
                    player = players[i + j]

                    with col:
                        # 创建球员卡片
                        with st.container():
                            st.markdown(f"### {player['name']}")

                            # 显示球员照片
                            if player['photo']:
                                team_upload_folder = get_team_upload_folder(current_team)
                                photo_path = os.path.join(team_upload_folder, player['photo'])
                                if os.path.exists(photo_path):
                                    st.image(photo_path, width=200)
                                else:
                                    st.info("照片文件不存在")
                            else:
                                st.info("暂无照片")

                            # 显示球员信息
                            st.write(f"**号码:** {player['jersey_number']}")
                            st.write(f"**加入时间:** {player['created_at'][:10]}")

                            # 操作按钮
                            if st.button(f"🗑️ 删除", key=f"delete_{player['id']}"):
                                if delete_player(current_team, player['id']):
                                    st.rerun()

                            st.markdown("---")
    else:
        st.info("还没有球员，点击\"添加球员\"开始建立您的球队")

if __name__ == "__main__":
    main()
