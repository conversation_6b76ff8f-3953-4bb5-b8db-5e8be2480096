# 📚 使用示例 (Usage Examples)

本文档提供了时尚换装工具包的详细使用示例。

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install requests pillow matplotlib numpy

# 克隆或下载工具包
# 确保所有文件都在同一目录下
```

### 2. 配置API密钥

编辑 `config.py` 文件：

```python
# 替换为您的实际API密钥
API_KEY = "sk-your-actual-api-key-here"
```

## 📸 单张照片处理示例

### 示例1: 命令行使用

```bash
# 基本用法
python single_fashion_tryon.py --model_image model.jpg --clothes_image clothes.png

# 使用完整路径
python single_fashion_tryon.py \
  --model_image /path/to/model/photo.jpg \
  --clothes_image /path/to/clothes/shirt.png
```

### 示例2: 交互式使用

```bash
# 运行脚本，然后按提示输入
python single_fashion_tryon.py

# 程序会提示：
# 请输入文件路径:
# 模特图片路径: photos/model.jpg
# 服装图片路径: clothes/shirt.png
```

### 示例3: 程序化调用

```python
import os
from single_fashion_tryon import *

# 设置文件路径
model_image = "photos/model.jpg"
clothes_image = "clothes/shirt.png"

# 创建输出目录
create_output_dirs()

# 执行完整流程
print("开始处理...")
step1_result = step1_fashion_tryon(model_image, clothes_image)
if step1_result:
    step2_result = step2_remove_background(step1_result)
    if step2_result:
        final_result = step3_add_white_background(step2_result)
        print(f"处理完成！结果保存在: {final_result}")
```

## 📁 批量处理示例

### 示例1: 批量处理文件夹

```bash
# 处理整个文件夹的照片
python batch_fashion_tryon.py \
  --input_folder photos/ \
  --clothes_image clothes/shirt.png

# 处理特定路径
python batch_fashion_tryon.py \
  --input_folder /Users/<USER>/photos \
  --clothes_image /Users/<USER>/clothes/dress.png
```

### 示例2: 批量处理不同服装

```bash
# 为不同照片使用不同服装
# 可以多次运行脚本，每次使用不同的服装

# 处理上衣换装
python batch_fashion_tryon.py \
  --input_folder photos/models \
  --clothes_image clothes/shirt.png

# 处理裙子换装  
python batch_fashion_tryon.py \
  --input_folder photos/models \
  --clothes_image clothes/dress.png
```

### 示例3: 程序化批量处理

```python
import os
from batch_fashion_tryon import *

# 设置路径
input_folder = "photos/batch_test"
clothes_image = "clothes/shirt.png"

# 获取所有图片文件
image_files = get_image_files(input_folder)
print(f"找到 {len(image_files)} 张图片")

# 批量处理
results = []
for image_file in image_files:
    photo_name = Path(image_file).stem
    result = process_single_photo(image_file, clothes_image, photo_name)
    results.append(result)
    
    if result["success"]:
        print(f"✅ {photo_name} 处理成功")
    else:
        print(f"❌ {photo_name} 处理失败")

# 统计结果
success_count = sum(1 for r in results if r["success"])
print(f"成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
```

## 🎯 实际应用场景

### 场景1: 电商产品展示

```bash
# 为电商平台生成标准化产品展示图
python batch_fashion_tryon.py \
  --input_folder product_photos/ \
  --clothes_image new_collection/shirt.png

# 结果：所有模特都穿上了新款衬衫，背景统一为白色
```

### 场景2: 时尚设计预览

```python
# 设计师预览新设计在不同模特上的效果
designs = ["design1.png", "design2.png", "design3.png"]
models = ["model1.jpg", "model2.jpg", "model3.jpg"]

for design in designs:
    for model in models:
        result = process_single_photo(model, design, f"{Path(model).stem}_{Path(design).stem}")
        if result["success"]:
            print(f"✅ {design} 在 {model} 上的效果已生成")
```

### 场景3: 证件照背景处理

```python
# 批量处理证件照，统一白底背景
def process_id_photos(photo_folder):
    """处理证件照，只进行背景处理"""
    image_files = get_image_files(photo_folder)
    
    for image_file in image_files:
        photo_name = Path(image_file).stem
        
        # 跳过换装步骤，直接处理背景
        # 这里可以直接调用背景移除和白底合成
        print(f"处理证件照: {photo_name}")
        
        # 移除背景
        temp_result = step2_remove_background(image_file, photo_name)
        if temp_result:
            # 添加白底
            final_result = step3_add_white_background(temp_result, photo_name)
            if final_result:
                print(f"✅ {photo_name} 白底背景处理完成")

# 使用示例
process_id_photos("id_photos/")
```

## 📊 结果分析示例

### 查看处理结果

```python
import json

# 读取批量处理报告
with open("analysis_reports/batch_test_report.json", "r", encoding="utf-8") as f:
    report = json.load(f)

# 分析成功率
total = report["summary"]["total_photos"]
success = report["summary"]["successful_photos"]
success_rate = report["summary"]["success_rate"]

print(f"处理统计:")
print(f"  总照片数: {total}")
print(f"  成功处理: {success}")
print(f"  成功率: {success_rate:.1f}%")

# 分析成本
total_cost_ptc = report["summary"]["total_cost_ptc"]
total_cost_cny = report["summary"]["total_cost_cny"]

print(f"成本统计:")
print(f"  总成本: {total_cost_ptc} PTC")
print(f"  人民币: {total_cost_cny:.1f} 元")
print(f"  平均成本: {total_cost_ptc/total:.1f} PTC/张")

# 分析处理时间
avg_time = report["summary"]["average_time_per_photo"]
total_time = report["summary"]["total_processing_time"]

print(f"时间统计:")
print(f"  平均处理时间: {avg_time:.1f} 秒/张")
print(f"  总处理时间: {total_time:.1f} 秒")
```

## 🔧 自定义配置示例

### 示例1: 修改输出目录

```python
# 在 config.py 中修改
OUTPUT_DIRS = {
    "temp": "my_temp",
    "results": "my_results", 
    "batch_results": "my_batch",
    "analysis": "my_reports"
}
```

### 示例2: 调整超时设置

```python
# 对于网络较慢的环境，增加超时时间
TIMEOUT_CONFIG = {
    "request_timeout": 60,        # 增加到60秒
    "task_max_wait": 1200,        # 增加到20分钟
    "task_check_interval": 45,    # 增加检查间隔
    "max_retry_attempts": 30      # 增加重试次数
}
```

### 示例3: 处理不同类型的服装

```python
# 处理裤子换装
FASHION_TRYON_CONFIG = {
    "modelImgSegLabels": "5",     # 5-裤子
    "clothesImgSegLabels": "5"    # 5-裤子
}

# 处理裙子换装
FASHION_TRYON_CONFIG = {
    "modelImgSegLabels": "6",     # 6-裙子
    "clothesImgSegLabels": "6"    # 6-裙子
}
```

## 🚨 常见问题解决

### 问题1: 图片格式不支持

```python
# 转换图片格式
from PIL import Image

def convert_to_supported_format(input_path, output_path):
    """将图片转换为支持的格式"""
    img = Image.open(input_path)
    if img.mode == 'RGBA':
        img = img.convert('RGB')
    img.save(output_path, 'JPEG', quality=95)
    return output_path

# 使用示例
converted_image = convert_to_supported_format("image.webp", "image.jpg")
```

### 问题2: 批量处理中断恢复

```python
def resume_batch_processing(report_file, input_folder, clothes_image):
    """从中断点恢复批量处理"""
    
    # 读取之前的报告
    if os.path.exists(report_file):
        with open(report_file, 'r', encoding='utf-8') as f:
            previous_report = json.load(f)
        
        # 获取已处理的文件
        processed_files = [r["model_image"] for r in previous_report["results"]]
    else:
        processed_files = []
    
    # 获取所有文件
    all_files = get_image_files(input_folder)
    
    # 找出未处理的文件
    remaining_files = [f for f in all_files if f not in processed_files]
    
    print(f"发现 {len(remaining_files)} 个未处理的文件")
    
    # 继续处理
    for file_path in remaining_files:
        photo_name = Path(file_path).stem
        result = process_single_photo(file_path, clothes_image, photo_name)
        print(f"处理完成: {photo_name}")

# 使用示例
resume_batch_processing(
    "analysis_reports/batch_test_report.json",
    "photos/",
    "clothes/shirt.png"
)
```

## 📈 性能优化建议

### 1. 图片预处理

```python
def optimize_image_for_processing(input_path, output_path, max_size=1024):
    """优化图片以提高处理速度"""
    img = Image.open(input_path)
    
    # 调整尺寸
    if max(img.size) > max_size:
        ratio = max_size / max(img.size)
        new_size = tuple(int(dim * ratio) for dim in img.size)
        img = img.resize(new_size, Image.Resampling.LANCZOS)
    
    # 转换格式
    if img.mode == 'RGBA':
        img = img.convert('RGB')
    
    # 保存优化后的图片
    img.save(output_path, 'JPEG', quality=90, optimize=True)
    return output_path
```

### 2. 并发处理 (谨慎使用)

```python
import concurrent.futures
import threading

# 注意：由于API限制，建议并发数设为1
def concurrent_batch_processing(image_files, clothes_image, max_workers=1):
    """并发批量处理 (谨慎使用)"""
    
    results = []
    lock = threading.Lock()
    
    def process_with_lock(image_file):
        photo_name = Path(image_file).stem
        result = process_single_photo(image_file, clothes_image, photo_name)
        
        with lock:
            results.append(result)
            print(f"完成: {photo_name}")
        
        return result
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = [executor.submit(process_with_lock, img) for img in image_files]
        concurrent.futures.wait(futures)
    
    return results
```

这些示例涵盖了工具包的主要使用场景。根据您的具体需求，可以参考相应的示例进行调整和优化。
