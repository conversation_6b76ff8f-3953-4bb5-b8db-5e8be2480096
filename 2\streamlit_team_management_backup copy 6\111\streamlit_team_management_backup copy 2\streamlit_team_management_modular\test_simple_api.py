#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的API测试 - 参考原始备份文件
Simple API Test - Reference Original Backup Files
"""

import requests
import time
import os
from PIL import Image

# API配置 - 从原始备份文件复制
API_KEY = "sk-Y5BDQdWlSCr2nQUYvbeKhEIwzzzg5AzGq8nTFR7KArlQuM3o"
BASE_URL = "https://api.302.ai"

# 换装参数
FASHION_TRYON_CONFIG = {
    "modelImgSegLabels": "10",    # 10-上衣, 5-裤子, 6-裙子
    "clothesImgSegLabels": "10"   # 10-上衣, 5-裤子, 6-裙子
}

# 超时配置
TIMEOUT_CONFIG = {
    "request_timeout": 30,        # 单次请求超时
    "task_max_wait": 600,         # 任务最大等待时间 (10分钟)
    "task_check_interval": 30,    # 任务状态检查间隔
    "max_retry_attempts": 20      # 最大重试次数
}

def create_test_images():
    """创建测试图片"""
    print("🎨 创建测试图片...")
    
    os.makedirs("test_simple", exist_ok=True)
    
    # 创建一个简单的服装图片
    clothes_img = Image.new('RGB', (512, 512), color='blue')
    clothes_path = "test_simple/blue_shirt.png"
    clothes_img.save(clothes_path)
    print(f"👕 创建服装图片: {clothes_path}")
    
    # 查找现有的球员照片
    player_image = None
    
    # 从uploads目录查找
    uploads_dir = "uploads"
    if os.path.exists(uploads_dir):
        for team_dir in os.listdir(uploads_dir):
            team_path = os.path.join(uploads_dir, team_dir)
            if os.path.isdir(team_path):
                for file in os.listdir(team_path):
                    if file.endswith('.jpg'):
                        player_image = os.path.join(team_path, file)
                        break
                if player_image:
                    break
    
    if player_image:
        print(f"📸 找到球员照片: {player_image}")
    else:
        print("❌ 没有找到球员照片")
    
    return player_image, clothes_path

def step1_fashion_tryon(model_image, clothes_image):
    """步骤1: 302.AI-ComfyUI 换装"""
    print("\n" + "="*60)
    print("🎯 步骤1: 302.AI-ComfyUI 换装")
    print("="*60)
    
    if not os.path.exists(model_image):
        print(f"❌ 模特图片不存在: {model_image}")
        return None
    
    if not os.path.exists(clothes_image):
        print(f"❌ 服装图片不存在: {clothes_image}")
        return None
    
    print(f"📸 模特图片: {model_image}")
    print(f"👕 服装图片: {clothes_image}")
    
    url = f"{BASE_URL}/302/comfyui/clothes-changer/create-task"
    headers = {"Authorization": f"Bearer {API_KEY}"}
    
    try:
        with open(model_image, 'rb') as model_file, open(clothes_image, 'rb') as clothes_file:
            files = {
                'modelImageFile': (os.path.basename(model_image), model_file, 'image/jpeg'),
                'clothesImageFile': (os.path.basename(clothes_image), clothes_file, 'image/png')
            }
            
            data = FASHION_TRYON_CONFIG
            
            print("📤 发送换装任务请求...")
            response = requests.post(url, headers=headers, files=files, data=data, 
                                   timeout=TIMEOUT_CONFIG["request_timeout"])
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None
    
    print(f"📊 响应状态码: {response.status_code}")
    
    if response.status_code in [200, 201]:
        try:
            result = response.json()
            print(f"📄 响应内容: {result}")
            
            if result.get('code') == 200 and 'data' in result:
                task_id = result['data']['taskId']
                print(f"✅ 换装任务创建成功！任务ID: {task_id}")
                return wait_for_fashion_task(task_id)
            else:
                print(f"❌ 任务创建失败: {result}")
                return None
        except Exception as e:
            print(f"❌ 解析响应失败: {e}")
            return None
    else:
        print(f"❌ API请求失败: {response.status_code}")
        print(f"📄 响应内容: {response.text}")
        return None

def wait_for_fashion_task(task_id):
    """等待换装任务完成"""
    print(f"⏳ 等待换装任务完成...")
    
    url = f"{BASE_URL}/302/comfyui/clothes-changer/check-task-status"
    headers = {"Authorization": f"Bearer {API_KEY}"}
    
    max_attempts = TIMEOUT_CONFIG["max_retry_attempts"]
    attempt = 0
    
    while attempt < max_attempts:
        attempt += 1
        print(f"🔄 第{attempt}次查询...")
        
        params = {"taskId": task_id}
        try:
            response = requests.get(url, headers=headers, params=params, 
                                  timeout=TIMEOUT_CONFIG["request_timeout"])
        except Exception as e:
            print(f"❌ 网络请求失败: {e}")
            time.sleep(TIMEOUT_CONFIG["task_check_interval"])
            continue
        
        if response.status_code == 200:
            try:
                result = response.json()
                status = result.get('data', 'UNKNOWN')
                
                print(f"📈 任务状态: {status}")
                
                if status == 'SUCCESS' and 'output' in result:
                    output = result['output']
                    result_url = output.get('resultUrl', '')
                    
                    print(f"🎉 换装任务完成！")
                    print(f"🔗 结果图URL: {result_url}")
                    
                    return download_image(result_url, "step1_fashion_result.png")
                    
                elif status in ['RUNNING', 'QUEUED', 'SUBMITTING']:
                    print(f"⏳ 任务进行中... 等待{TIMEOUT_CONFIG['task_check_interval']}秒后再次查询...")
                    time.sleep(TIMEOUT_CONFIG["task_check_interval"])
                    
                else:
                    print(f"❌ 任务失败，状态: {status}")
                    print(f"📄 完整响应: {result}")
                    return None
                    
            except Exception as e:
                print(f"❌ 解析响应失败: {e}")
                time.sleep(TIMEOUT_CONFIG["task_check_interval"])
                continue
        else:
            print(f"❌ 查询失败: {response.status_code}")
            print(f"📄 响应内容: {response.text}")
            time.sleep(TIMEOUT_CONFIG["task_check_interval"])
            continue
    
    print("⏰ 等待超时")
    return None

def download_image(url, filename):
    """下载图片"""
    try:
        print(f"📥 下载图片: {url}")
        response = requests.get(url, timeout=30)
        if response.status_code == 200:
            output_path = os.path.join("test_simple", filename)
            
            with open(output_path, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ 图片下载成功: {output_path}")
            return output_path
        else:
            print(f"❌ 下载图片失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 下载图片异常: {e}")
        return None

def main():
    """主测试函数"""
    print("🧪 简单API测试开始")
    print("=" * 60)
    
    # 创建测试图片
    player_image, clothes_image = create_test_images()
    
    if not player_image:
        print("❌ 没有找到球员照片，无法进行测试")
        return
    
    # 测试换装API
    print(f"\n🚀 开始测试换装API")
    print(f"📸 模特图片: {player_image}")
    print(f"👕 服装图片: {clothes_image}")
    
    result = step1_fashion_tryon(player_image, clothes_image)
    
    if result:
        print(f"\n🎉 测试成功！")
        print(f"📁 结果文件: {result}")
        
        # 检查文件是否存在
        if os.path.exists(result):
            file_size = os.path.getsize(result)
            print(f"📊 文件大小: {file_size} 字节")
        else:
            print(f"❌ 结果文件不存在: {result}")
    else:
        print(f"\n❌ 测试失败")

if __name__ == "__main__":
    main()
