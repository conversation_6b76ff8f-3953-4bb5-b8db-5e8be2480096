import java.io.File;

/**
 * 命令行入口类
 * 专为Python Subprocess调用设计
 * 接收JSON文件路径，生成Word报名表，返回结果
 */
public class CommandLineMain {
    
    public static void main(String[] args) {
        try {
            // 检查命令行参数
            if (args.length < 1) {
                System.err.println("ERROR:Usage: java CommandLineMain <json-file-path>");
                System.exit(1);
            }
            
            String jsonFilePath = args[0];
            
            // 验证JSON文件存在
            File jsonFile = new File(jsonFilePath);
            if (!jsonFile.exists()) {
                System.err.println("ERROR:JSON file not found: " + jsonFilePath);
                System.exit(1);
            }
            
            System.err.println("INFO:Starting Word generation from JSON: " + jsonFilePath);
            
            // 解析JSON数据
            FootballTeamData teamData = JsonDataParser.parseFromJson(jsonFilePath);
            if (teamData == null) {
                System.err.println("ERROR:Failed to parse JSON data");
                System.exit(1);
            }
            
            System.err.println("INFO:JSON data parsed successfully");
            System.err.println("INFO:Team: " + teamData.getTeamInfo().getOrganizationName());
            System.err.println("INFO:Players: " + teamData.getPlayerCount());
            
            // 从JSON中获取配置信息
            JsonDataParser.ConfigInfo config = JsonDataParser.getConfigFromJson(jsonFilePath);
            
            // 创建Word生成器
            WordGeneratorCore generator = new WordGeneratorCore(
                config.templatePath != null ? config.templatePath : "template.docx",
                config.outputDir != null ? config.outputDir : "output",
                config.photosDir != null ? config.photosDir : "photos"
            );
            
            System.err.println("INFO:WordGeneratorCore initialized");
            
            // 生成Word文档
            String outputPath = generator.generateReport(teamData);
            
            if (outputPath != null) {
                // 验证生成的文件存在
                File outputFile = new File(outputPath);
                if (outputFile.exists()) {
                    System.err.println("INFO:Word document generated successfully: " + outputPath);
                    // 输出成功结果到stdout（Python会读取这个输出）
                    System.out.println("SUCCESS:" + outputPath);
                    System.exit(0);
                } else {
                    System.err.println("ERROR:Generated file does not exist: " + outputPath);
                    System.exit(1);
                }
            } else {
                System.err.println("ERROR:Word generation returned null");
                System.exit(1);
            }
            
        } catch (Exception e) {
            System.err.println("ERROR:Exception during Word generation: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
    
    /**
     * 显示使用帮助
     */
    private static void showUsage() {
        System.err.println("Word Generator Command Line Tool");
        System.err.println("Usage: java CommandLineMain <json-file-path>");
        System.err.println("");
        System.err.println("Arguments:");
        System.err.println("  json-file-path    Path to JSON file containing team and player data");
        System.err.println("");
        System.err.println("JSON Format:");
        System.err.println("{");
        System.err.println("  \"teamInfo\": {");
        System.err.println("    \"title\": \"比赛标题\",");
        System.err.println("    \"organizationName\": \"单位名称\",");
        System.err.println("    \"teamLeader\": \"领队\",");
        System.err.println("    \"coach\": \"教练\",");
        System.err.println("    \"teamDoctor\": \"队医\"");
        System.err.println("  },");
        System.err.println("  \"players\": [");
        System.err.println("    {");
        System.err.println("      \"number\": \"10\",");
        System.err.println("      \"name\": \"球员姓名\",");
        System.err.println("      \"photoPath\": \"照片路径\"");
        System.err.println("    }");
        System.err.println("  ],");
        System.err.println("  \"config\": {");
        System.err.println("    \"templatePath\": \"template.docx\",");
        System.err.println("    \"outputDir\": \"output\",");
        System.err.println("    \"photosDir\": \"photos\"");
        System.err.println("  }");
        System.err.println("}");
    }
}
