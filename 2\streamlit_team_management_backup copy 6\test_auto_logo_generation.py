#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动队徽生成功能
Test Auto Logo Generation

验证AI在收集到球队信息后自动生成队徽的功能
"""

import os
import sys
from datetime import datetime

# 添加项目路径
sys.path.append('streamlit_team_management_modular')

def test_auto_logo_from_text_extraction():
    """测试从文本提取信息时自动生成队徽"""
    print("🧪 测试文本提取时自动队徽生成...")
    
    try:
        from services.enhanced_ai_service import enhanced_ai_service
        
        if not enhanced_ai_service.is_available():
            print("❌ AI服务不可用，跳过测试")
            return False
        
        # 模拟用户输入包含球队信息的文本
        test_text = """
        我们是雄鹰足球俱乐部，队长是张三，联系电话是13800138000。
        我们的球衣是红色的，希望参加这次比赛。
        领队是李四，队医是王五。
        """
        
        print(f"📝 测试文本: {test_text}")
        
        # 调用信息提取方法
        arguments = {"user_text": test_text}
        result = enhanced_ai_service._extract_team_info_from_text(arguments)
        
        print(f"📊 提取结果: {result}")
        
        # 检查是否成功提取信息
        if not result.get("success"):
            print(f"❌ 信息提取失败: {result.get('error')}")
            return False
        
        # 检查是否自动生成了队徽
        extracted_info = result.get("extracted_info", {})
        auto_logo = extracted_info.get("auto_generated_logo")
        
        if auto_logo:
            print("✅ 自动队徽生成成功！")
            print(f"   球队名称: {auto_logo.get('team_name')}")
            print(f"   是否有图像: {auto_logo.get('has_image')}")
            print(f"   文件路径: {auto_logo.get('logo_file_path')}")
            print(f"   触发方式: {auto_logo.get('auto_trigger')}")
            
            # 检查文件是否真的存在
            logo_path = auto_logo.get('logo_file_path')
            if logo_path and os.path.exists(logo_path):
                print(f"✅ 队徽文件已保存: {logo_path}")
            else:
                print(f"⚠️ 队徽文件不存在: {logo_path}")
            
            return True
        else:
            print("❌ 没有自动生成队徽")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_auto_logo_from_team_save():
    """测试保存球队信息时自动生成队徽"""
    print("\n🧪 测试保存球队信息时自动队徽生成...")
    
    try:
        from services.enhanced_ai_assistant import enhanced_ai_assistant
        
        # 模拟球队数据
        team_data = {
            "basic_info": {
                "team_name": "闪电足球队",
                "contact_person": "赵六",
                "contact_phone": "13900139000",
                "leader_name": "赵六",
                "team_doctor": "赵六"
            },
            "kit_colors": {
                "jersey_color": "蓝色和白色"
            }
        }
        
        print(f"📝 测试球队数据: {team_data}")
        
        # 调用保存方法
        arguments = {"team_data": team_data, "team_id": "test_team"}
        result = enhanced_ai_assistant._save_team_info(arguments)
        
        print(f"📊 保存结果: {result}")
        
        # 检查是否成功保存
        if not result.get("success"):
            print(f"❌ 球队信息保存失败: {result.get('message')}")
            return False
        
        # 检查是否自动生成了队徽
        saved_data = result.get("data", {})
        auto_logo = saved_data.get("auto_generated_logo")
        
        if auto_logo:
            print("✅ 保存时自动队徽生成成功！")
            print(f"   球队名称: {auto_logo.get('team_name')}")
            print(f"   是否有图像: {auto_logo.get('has_image')}")
            print(f"   文件路径: {auto_logo.get('logo_file_path')}")
            print(f"   触发方式: {auto_logo.get('auto_trigger')}")
            
            # 检查文件是否真的存在
            logo_path = auto_logo.get('logo_file_path')
            if logo_path and os.path.exists(logo_path):
                print(f"✅ 队徽文件已保存: {logo_path}")
            else:
                print(f"⚠️ 队徽文件不存在: {logo_path}")
            
            return True
        else:
            print("❌ 保存时没有自动生成队徽")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_auto_logo_conditions():
    """测试自动生成队徽的条件判断"""
    print("\n🧪 测试自动队徽生成条件...")
    
    try:
        from services.enhanced_ai_service import enhanced_ai_service
        
        if not enhanced_ai_service.is_available():
            print("❌ AI服务不可用，跳过测试")
            return False
        
        # 测试用例1: 有球队名称，有球衣颜色
        test_cases = [
            {
                "name": "完整信息",
                "data": {"球队名称": "测试队1", "球衣颜色": "红色"},
                "should_generate": True
            },
            {
                "name": "只有球队名称",
                "data": {"球队名称": "测试队2"},
                "should_generate": True  # 应该使用默认颜色
            },
            {
                "name": "没有球队名称",
                "data": {"球衣颜色": "蓝色"},
                "should_generate": False
            },
            {
                "name": "空球队名称",
                "data": {"球队名称": "", "球衣颜色": "绿色"},
                "should_generate": False
            }
        ]
        
        success_count = 0
        
        for case in test_cases:
            print(f"\n📋 测试用例: {case['name']}")
            print(f"   数据: {case['data']}")
            
            result = enhanced_ai_service._auto_generate_logo_if_possible(case['data'])
            
            if case['should_generate']:
                if result:
                    print(f"   ✅ 正确生成了队徽")
                    success_count += 1
                else:
                    print(f"   ❌ 应该生成队徽但没有生成")
            else:
                if not result:
                    print(f"   ✅ 正确没有生成队徽")
                    success_count += 1
                else:
                    print(f"   ❌ 不应该生成队徽但生成了")
        
        print(f"\n📊 条件测试结果: {success_count}/{len(test_cases)} 通过")
        return success_count == len(test_cases)
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def check_logo_folder():
    """检查队徽文件夹"""
    print("\n🧪 检查队徽文件夹...")
    
    logo_folder = "streamlit_team_management_modular/data/team_logos"
    
    if os.path.exists(logo_folder):
        print(f"✅ 队徽文件夹存在: {logo_folder}")
        
        # 列出文件夹中的文件
        files = os.listdir(logo_folder)
        print(f"📁 文件夹中有 {len(files)} 个文件:")
        
        for file in files:
            file_path = os.path.join(logo_folder, file)
            file_size = os.path.getsize(file_path)
            print(f"   - {file} ({file_size} bytes)")
        
        return True
    else:
        print(f"❌ 队徽文件夹不存在: {logo_folder}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试AI自动队徽生成功能...")
    print("=" * 60)
    
    # 测试列表
    tests = [
        ("检查队徽文件夹", check_logo_folder),
        ("自动队徽生成条件", test_auto_logo_conditions),
        ("文本提取时自动生成", test_auto_logo_from_text_extraction),
        ("保存时自动生成", test_auto_logo_from_team_save)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 自动队徽生成测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有测试通过！自动队徽生成功能正常工作！")
        print("\n💡 现在当用户向AI提供球队信息时，系统会自动生成队徽并保存到文件夹中。")
    else:
        print(f"\n⚠️ 还有 {total-passed} 个问题需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    main()
