#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片处理工具
Image Processing Utils

提供图片处理相关的工具函数
"""

import os
import uuid
from typing import Tuple, Optional
from PIL import Image
import streamlit as st


class ImageProcessor:
    """图片处理器"""
    
    @staticmethod
    def process_uploaded_image(image_file, save_path: str, 
                             max_size: Tuple[int, int] = (800, 800)) -> bool:
        """
        处理上传的图片：压缩和优化
        
        Args:
            image_file: 上传的图片文件
            save_path: 保存路径
            max_size: 最大尺寸 (width, height)
            
        Returns:
            bool: 处理是否成功
        """
        try:
            # 打开图片
            img = Image.open(image_file)
            
            # 转换为RGB模式（如果需要）
            if img.mode in ('RGBA', 'P'):
                img = img.convert('RGB')
            
            # 计算新尺寸（保持宽高比）
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # 确保保存目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            # 保存优化后的图片
            img.save(save_path, 'JPEG', quality=85, optimize=True)
            
            return True
        except Exception as e:
            st.error(f"图片处理失败: {e}")
            return False
    
    @staticmethod
    def generate_unique_filename(original_filename: str) -> str:
        """
        生成唯一的文件名
        
        Args:
            original_filename: 原始文件名
            
        Returns:
            str: 唯一的文件名
        """
        file_extension = original_filename.split('.')[-1].lower()
        return f"{uuid.uuid4().hex}.{file_extension}"
    
    @staticmethod
    def validate_image_file(file) -> bool:
        """
        验证图片文件
        
        Args:
            file: 上传的文件
            
        Returns:
            bool: 文件是否有效
        """
        if file is None:
            return False
        
        # 检查文件扩展名
        filename = file.name.lower()
        valid_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp']
        
        if not any(filename.endswith(ext) for ext in valid_extensions):
            return False
        
        try:
            # 尝试打开图片验证格式
            img = Image.open(file)
            img.verify()
            return True
        except Exception:
            return False
    
    @staticmethod
    def get_image_info(image_path: str) -> Optional[dict]:
        """
        获取图片信息
        
        Args:
            image_path: 图片路径
            
        Returns:
            dict: 图片信息，如果失败返回None
        """
        try:
            if not os.path.exists(image_path):
                return None
            
            with Image.open(image_path) as img:
                return {
                    'size': img.size,
                    'mode': img.mode,
                    'format': img.format,
                    'file_size': os.path.getsize(image_path)
                }
        except Exception:
            return None
    
    @staticmethod
    def resize_image(image_path: str, output_path: str, 
                    size: Tuple[int, int], quality: int = 85) -> bool:
        """
        调整图片尺寸
        
        Args:
            image_path: 输入图片路径
            output_path: 输出图片路径
            size: 目标尺寸 (width, height)
            quality: 图片质量 (1-100)
            
        Returns:
            bool: 是否成功
        """
        try:
            with Image.open(image_path) as img:
                # 转换为RGB模式（如果需要）
                if img.mode in ('RGBA', 'P'):
                    img = img.convert('RGB')
                
                # 调整尺寸（保持宽高比）
                img.thumbnail(size, Image.Resampling.LANCZOS)
                
                # 确保输出目录存在
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
                
                # 保存图片
                img.save(output_path, 'JPEG', quality=quality, optimize=True)
                
                return True
        except Exception as e:
            st.error(f"图片尺寸调整失败: {e}")
            return False
