#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能缓存性能测试
Smart Cache Performance Test

测试智能缓存系统的性能提升效果
"""

import sys
import os
import time
import json
from typing import Dict, Any, List
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_cache_manager_import():
    """测试缓存管理器导入"""
    print("🔍 测试智能缓存管理器导入...")
    
    try:
        from utils.smart_cache_manager import SmartCacheManager, CacheConfig, smart_cache
        from utils.smart_cache_manager import cache_critical, cache_important, cache_normal
        
        print("✅ 智能缓存管理器导入成功")
        
        # 测试实例化
        manager = SmartCacheManager()
        assert hasattr(manager, 'smart_cache')
        assert hasattr(manager, 'generate_cache_key')
        assert hasattr(manager, 'get_cache_stats')
        print("✅ 缓存管理器方法检查通过")
        
        return True
    except Exception as e:
        print(f"❌ 缓存管理器导入失败: {e}")
        return False

def test_cache_decorators():
    """测试缓存装饰器"""
    print("\n🎯 测试缓存装饰器...")
    
    try:
        from utils.smart_cache_manager import cache_normal, smart_cache
        
        # 模拟Session State
        import streamlit as st
        if not hasattr(st, 'session_state'):
            class MockSessionState:
                def __init__(self):
                    self.data = {}
                def get(self, key, default=None):
                    return self.data.get(key, default)
                def __setitem__(self, key, value):
                    self.data[key] = value
                def __getitem__(self, key):
                    return self.data[key]
                def __contains__(self, key):
                    return key in self.data
            st.session_state = MockSessionState()
        
        # 测试函数
        call_count = 0
        
        @cache_normal(ttl=60)
        def expensive_function(x: int) -> int:
            nonlocal call_count
            call_count += 1
            time.sleep(0.1)  # 模拟耗时操作
            return x * 2
        
        # 第一次调用
        start_time = time.time()
        result1 = expensive_function(5)
        first_call_time = time.time() - start_time
        
        # 第二次调用（应该命中缓存）
        start_time = time.time()
        result2 = expensive_function(5)
        second_call_time = time.time() - start_time
        
        # 验证结果
        assert result1 == result2 == 10
        assert call_count == 1  # 只调用了一次
        assert second_call_time < first_call_time  # 缓存调用更快
        
        print("✅ 缓存装饰器测试通过")
        print(f"   第一次调用: {first_call_time:.3f}秒")
        print(f"   第二次调用: {second_call_time:.3f}秒")
        print(f"   性能提升: {((first_call_time - second_call_time) / first_call_time * 100):.1f}%")
        
        return True
    except Exception as e:
        print(f"❌ 缓存装饰器测试失败: {e}")
        return False

def test_cache_key_generation():
    """测试缓存键生成"""
    print("\n🔑 测试缓存键生成...")
    
    try:
        from utils.smart_cache_manager import SmartCacheManager, CacheConfig
        
        manager = SmartCacheManager()
        config = CacheConfig(user_isolated=True, version_sensitive=True)
        
        # 测试不同参数生成不同的键
        key1 = manager.generate_cache_key("test_func", (1, 2), {"a": "b"}, config)
        key2 = manager.generate_cache_key("test_func", (1, 3), {"a": "b"}, config)
        key3 = manager.generate_cache_key("test_func", (1, 2), {"a": "c"}, config)
        
        assert key1 != key2
        assert key1 != key3
        assert key2 != key3
        
        print("✅ 缓存键生成测试通过")
        print(f"   键1: {key1}")
        print(f"   键2: {key2}")
        print(f"   键3: {key3}")
        
        return True
    except Exception as e:
        print(f"❌ 缓存键生成测试失败: {e}")
        return False

def test_cache_ttl():
    """测试缓存TTL（生存时间）"""
    print("\n⏰ 测试缓存TTL...")
    
    try:
        from utils.smart_cache_manager import cache_normal, smart_cache
        
        call_count = 0
        
        @cache_normal(ttl=1)  # 1秒TTL
        def short_ttl_function(x: int) -> int:
            nonlocal call_count
            call_count += 1
            return x * 3
        
        # 第一次调用
        result1 = short_ttl_function(10)
        assert call_count == 1
        
        # 立即第二次调用（应该命中缓存）
        result2 = short_ttl_function(10)
        assert call_count == 1  # 仍然是1
        assert result1 == result2
        
        # 等待TTL过期
        time.sleep(1.1)
        
        # 第三次调用（缓存应该过期）
        result3 = short_ttl_function(10)
        assert call_count == 2  # 应该增加到2
        assert result1 == result3
        
        print("✅ 缓存TTL测试通过")
        print(f"   TTL过期前调用次数: 1")
        print(f"   TTL过期后调用次数: 2")
        
        return True
    except Exception as e:
        print(f"❌ 缓存TTL测试失败: {e}")
        return False

def test_cache_stats():
    """测试缓存统计"""
    print("\n📊 测试缓存统计...")
    
    try:
        from utils.smart_cache_manager import cache_normal, smart_cache
        
        # 重置统计
        smart_cache.cache_stats = {"hits": 0, "misses": 0, "evictions": 0}
        
        @cache_normal(ttl=60)
        def stats_test_function(x: int) -> int:
            return x * 4
        
        # 第一次调用（应该是miss）
        stats_test_function(1)
        
        # 第二次调用（应该是hit）
        stats_test_function(1)
        
        # 第三次调用不同参数（应该是miss）
        stats_test_function(2)
        
        # 获取统计信息
        stats = smart_cache.get_cache_stats()
        
        assert stats["total_hits"] >= 1
        assert stats["total_misses"] >= 2
        
        print("✅ 缓存统计测试通过")
        print(f"   命中率: {stats['hit_rate']}")
        print(f"   总命中: {stats['total_hits']}")
        print(f"   总未命中: {stats['total_misses']}")
        
        return True
    except Exception as e:
        print(f"❌ 缓存统计测试失败: {e}")
        return False

def test_service_integration():
    """测试服务集成"""
    print("\n🔗 测试服务集成...")
    
    try:
        # 测试TeamService缓存
        from services.team_service import TeamService
        
        team_service = TeamService()
        
        # 测试get_teams_list缓存
        start_time = time.time()
        teams1 = team_service.get_teams_list()
        first_call_time = time.time() - start_time
        
        start_time = time.time()
        teams2 = team_service.get_teams_list()
        second_call_time = time.time() - start_time
        
        assert teams1 == teams2
        # 第二次调用应该更快（缓存命中）
        if second_call_time < first_call_time:
            print("✅ TeamService缓存生效")
        else:
            print("⚠️ TeamService缓存可能未生效（可能是首次运行）")
        
        print(f"   第一次调用: {first_call_time:.3f}秒")
        print(f"   第二次调用: {second_call_time:.3f}秒")
        
        return True
    except Exception as e:
        print(f"❌ 服务集成测试失败: {e}")
        return False

def test_performance_benchmark():
    """性能基准测试"""
    print("\n🚀 性能基准测试...")
    
    try:
        from utils.smart_cache_manager import cache_normal
        
        # 无缓存版本
        def slow_function_no_cache(n: int) -> int:
            time.sleep(0.01)  # 模拟10ms的计算
            return sum(range(n))
        
        # 有缓存版本
        @cache_normal(ttl=300)
        def slow_function_with_cache(n: int) -> int:
            time.sleep(0.01)  # 模拟10ms的计算
            return sum(range(n))
        
        # 测试数据
        test_values = [100, 200, 100, 300, 200, 100]  # 有重复值
        
        # 无缓存性能测试
        start_time = time.time()
        results_no_cache = [slow_function_no_cache(n) for n in test_values]
        no_cache_time = time.time() - start_time
        
        # 有缓存性能测试
        start_time = time.time()
        results_with_cache = [slow_function_with_cache(n) for n in test_values]
        with_cache_time = time.time() - start_time
        
        # 验证结果一致性
        assert results_no_cache == results_with_cache
        
        # 计算性能提升
        performance_improvement = ((no_cache_time - with_cache_time) / no_cache_time) * 100
        
        print("✅ 性能基准测试完成")
        print(f"   无缓存时间: {no_cache_time:.3f}秒")
        print(f"   有缓存时间: {with_cache_time:.3f}秒")
        print(f"   性能提升: {performance_improvement:.1f}%")
        
        return True
    except Exception as e:
        print(f"❌ 性能基准测试失败: {e}")
        return False

def print_performance_summary():
    """打印性能总结"""
    print("\n" + "="*60)
    print("🎯 智能缓存性能优化总结")
    print("="*60)
    
    print("""
🚀 缓存策略:
  • L1缓存: Session State（会话级）
  • L2缓存: Streamlit Cache（应用级）
  • L3缓存: 文件系统（持久化）

💡 缓存配置:
  • 关键数据: 15分钟TTL，高优先级
  • 重要数据: 10分钟TTL，中优先级
  • 普通数据: 5分钟TTL，普通优先级

📊 预期性能提升:
  • 球队列表加载: 80-90%提升
  • 球员数据加载: 70-85%提升
  • AI响应缓存: 95%+提升（重复查询）
  • 图片处理结果: 60-80%提升

🔧 智能特性:
  • 用户隔离缓存
  • 版本敏感更新
  • 自动TTL管理
  • LRU淘汰策略
  • 优先级保护

📱 使用方式:
  • 主应用: streamlit run app.py
  • 缓存管理: 侧边栏"显示缓存管理"
  • 性能测试: python test_smart_cache_performance.py
""")

def main():
    """主测试函数"""
    print("🧪 智能缓存性能测试")
    print("="*50)
    
    tests = [
        ("缓存管理器导入", test_cache_manager_import),
        ("缓存装饰器", test_cache_decorators),
        ("缓存键生成", test_cache_key_generation),
        ("缓存TTL", test_cache_ttl),
        ("缓存统计", test_cache_stats),
        ("服务集成", test_service_integration),
        ("性能基准", test_performance_benchmark)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！智能缓存系统运行正常！")
        print_performance_summary()
    else:
        print("⚠️ 部分测试失败，请检查缓存实现")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
