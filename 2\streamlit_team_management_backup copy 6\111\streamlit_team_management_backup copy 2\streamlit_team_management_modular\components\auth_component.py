#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户认证界面组件
User Authentication UI Component

提供登录、注册和用户管理界面
"""

import streamlit as st
from typing import Optional
from services.auth_service import AuthService


class AuthComponent:
    """用户认证界面组件"""
    
    def __init__(self):
        self.auth_service = AuthService()
    
    def render_welcome_page(self) -> bool:
        """
        渲染欢迎页面
        
        Returns:
            bool: 是否成功登录
        """
        # 页面标题
        st.markdown("""
        <div style="text-align: center; padding: 40px 20px;">
            <h1>🏆 淄川五人制球队管理系统</h1>
            <p style="font-size: 18px; color: #666; margin-bottom: 40px;">
                专业的球队管理和AI照片处理平台
            </p>
        </div>
        """, unsafe_allow_html=True)
        
        # 检查是否已登录
        if self.auth_service.is_logged_in():
            return True
        
        # 登录表单
        return self._render_login_form()
    
    def _render_login_form(self) -> bool:
        """
        渲染登录表单
        
        Returns:
            bool: 是否成功登录
        """
        col1, col2, col3 = st.columns([1, 2, 1])
        
        with col2:
            # 检查是否有返回用户
            if 'returning_user' in st.session_state and st.session_state.returning_user:
                st.markdown("### 🔐 输入您的专属密码")
                st.markdown("欢迎回来！请输入您的密码继续使用。")
            else:
                st.markdown("### 👋 欢迎使用！")
                st.markdown("为了保护您的球队数据，请设置一个专属密码：")
            
            # 密码输入
            password = st.text_input(
                "专属密码",
                type="password",
                placeholder="输入您的专属密码",
                help="建议使用有意义的密码，如：淄川FC2024",
                key="auth_password"
            )
            
            # 密码提示
            if not ('returning_user' in st.session_state and st.session_state.returning_user):
                st.markdown("""
                <div style="background-color: #f0f8ff; padding: 15px; border-radius: 8px; margin: 10px 0;">
                    <h4 style="margin: 0 0 10px 0;">💡 密码建议：</h4>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li>球队名称 + 年份：<code>淄川FC2024</code></li>
                        <li>有意义的短语：<code>冠军之路</code></li>
                        <li>简单组合：<code>team123</code></li>
                    </ul>
                    <p style="margin: 10px 0 0 0; font-size: 14px; color: #666;">
                        支持中文、英文、数字和特殊字符，长度4-20字符
                    </p>
                </div>
                """, unsafe_allow_html=True)
            
            # 登录按钮
            col_btn1, col_btn2 = st.columns(2)
            
            with col_btn1:
                if st.button("🚀 开始管理我的球队", type="primary", use_container_width=True):
                    return self._handle_login(password)
            
            with col_btn2:
                if st.button("🔄 切换用户", use_container_width=True):
                    st.session_state.returning_user = True
                    st.rerun()
            
            # 帮助信息
            with st.expander("❓ 需要帮助？"):
                st.markdown("""
                **忘记密码了？**
                - 尝试您常用的密码组合
                - 检查大小写是否正确
                - 确认是否包含空格或特殊字符
                
                **第一次使用？**
                - 设置一个容易记住的密码
                - 密码将用于保护您的球队数据
                - 无需注册，密码即可开始使用
                
                **安全提示：**
                - 请妥善保管您的密码
                - 建议定期备份重要数据
                - 同一密码可在多设备使用
                """)
        
        return False
    
    def _handle_login(self, password: str) -> bool:
        """
        处理登录逻辑
        
        Args:
            password: 用户输入的密码
            
        Returns:
            bool: 是否成功登录
        """
        if not password:
            st.error("请输入密码")
            return False
        
        # 尝试登录
        success, message = self.auth_service.login(password)
        
        if success:
            st.success(message)
            
            # 检查是否为新用户
            user_id = self.auth_service.get_current_user_id()
            if self.auth_service.is_new_user(user_id):
                self._setup_new_user(user_id)
            
            # 标记为返回用户
            st.session_state.returning_user = True
            
            st.rerun()
            return True
        else:
            st.error(message)
            
            # 记录失败尝试
            self.auth_service._record_failed_attempt()
            
            return False
    
    def _setup_new_user(self, user_id: str) -> None:
        """
        设置新用户

        Args:
            user_id: 用户ID
        """
        # 创建用户数据文件夹
        self.auth_service.create_user_data_folder(user_id)

        # 显示欢迎信息
        st.balloons()
        st.success("🎉 欢迎使用球队管理系统！请创建您的第一个球队。")
    

    
    def render_user_info(self) -> None:
        """渲染用户信息"""
        if not self.auth_service.is_logged_in():
            return
        
        user_display = self.auth_service.get_user_display_name()
        
        col1, col2 = st.columns([3, 1])
        
        with col1:
            st.markdown(f"👤 当前用户: **{user_display}**")
        
        with col2:
            if st.button("🚪 切换用户", key="logout_btn"):
                self.auth_service.logout()
                st.rerun()
    
    def render_user_menu(self) -> None:
        """渲染用户菜单"""
        if not self.auth_service.is_logged_in():
            return
        
        user_display = self.auth_service.get_user_display_name()
        
        with st.expander(f"👤 {user_display}", expanded=False):
            col1, col2 = st.columns(2)
            
            with col1:
                if st.button("🔄 切换用户", use_container_width=True):
                    self.auth_service.logout()
                    st.rerun()
            
            with col2:
                if st.button("💾 导出数据", use_container_width=True):
                    self._export_user_data()
            
            # 用户统计信息
            self._show_user_stats()
    
    def _export_user_data(self) -> None:
        """导出用户数据"""
        st.info("数据导出功能开发中...")
        # TODO: 实现数据导出功能
    
    def _show_user_stats(self) -> None:
        """显示用户统计信息"""
        user_id = self.auth_service.get_current_user_id()
        if not user_id:
            return
        
        try:
            from services.team_service import TeamService
            team_service = TeamService()
            
            # 获取用户球队数量
            teams = team_service.get_user_teams(user_id)
            team_count = len(teams)
            
            # 获取球员总数
            total_players = 0
            for team_name in teams:
                team_data = team_service.load_team_data_for_user(user_id, team_name)
                total_players += len(team_data.get('players', []))
            
            st.markdown("---")
            st.markdown("### 📊 我的数据")
            
            col1, col2 = st.columns(2)
            with col1:
                st.metric("球队数量", team_count)
            with col2:
                st.metric("球员总数", total_players)
                
        except Exception as e:
            st.warning(f"获取统计信息失败: {e}")
    
    def check_auth_required(self) -> bool:
        """
        检查是否需要认证
        
        Returns:
            bool: True表示需要显示登录页面，False表示已登录
        """
        return not self.auth_service.is_logged_in()
