# Fetch（获取任务）

## OpenAPI Specification

```yaml
openapi: 3.0.1
info:
  title: ''
  description: ''
  version: 1.0.0
paths:
  /mj-turbo/task/{id}/fetch:
    get:
      summary: Fetch（获取任务）
      deprecated: false
      description: '**价格：0 PTC/次**'
      operationId: fetchUsingGET_1
      tags:
        - 图片生成/Midjourney-Turbo
        - 任务查询
      parameters:
        - name: id
          in: path
          description: 任务ID
          required: true
          example: '1'
          schema:
            type: string
        - name: mj-api-secret
          in: header
          description: |
            302.AI后台的API Key
          required: true
          example: '{{YOUR_API_KEY}}'
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/%E4%BB%BB%E5%8A%A1'
          headers: {}
          x-apifox-name: OK
        '401':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties: {}
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          headers: {}
          x-apifox-name: Unauthorized
        '403':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties: {}
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          headers: {}
          x-apifox-name: Forbidden
        '404':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties: {}
                x-apifox-orders: []
                x-apifox-ignore-properties: []
          headers: {}
          x-apifox-name: Not Found
      security: []
      x-apifox-folder: 图片生成/Midjourney-Turbo
      x-apifox-status: released
      x-run-in-apifox: https://app.apifox.com/web/project/4012774/apis/api-329176693-run
components:
  schemas:
    任务:
      type: object
      properties:
        action:
          type: string
          description: 任务类型
          enum:
            - IMAGINE
            - UPSCALE
            - VARIATION
            - ZOOM
            - PAN
            - DESCRIBE
            - BLEND
            - SHORTEN
        buttons:
          type: array
          items:
            $ref: '#/components/schemas/%E5%8F%AF%E6%89%A7%E8%A1%8C%E6%8C%89%E9%92%AE'
        description:
          type: string
          description: 任务描述
        failReason:
          type: string
          description: 失败原因
        finishTime:
          type: integer
          format: int64
          description: 结束时间
        id:
          type: string
          description: ID
        imageUrl:
          type: string
          description: 图片url
        progress:
          type: string
          description: 任务进度
        prompt:
          type: string
          description: 提示词
        promptEn:
          type: string
          description: 提示词-英文
        properties:
          type: object
          x-apifox-orders: []
          properties: {}
          x-apifox-ignore-properties: []
        startTime:
          type: integer
          format: int64
          description: 开始执行时间
        state:
          type: string
          description: 自定义参数
        status:
          type: string
          description: 任务状态
          enum:
            - NOT_START
            - SUBMITTED
            - MODAL
            - IN_PROGRESS
            - FAILURE
            - SUCCESS
            - CANCEL
        submitTime:
          type: integer
          format: int64
          description: 提交时间
      title: 任务
      x-apifox-orders:
        - action
        - buttons
        - description
        - failReason
        - finishTime
        - id
        - imageUrl
        - progress
        - prompt
        - promptEn
        - properties
        - startTime
        - state
        - status
        - submitTime
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    可执行按钮:
      type: object
      properties:
        customId:
          type: string
          description: 动作标识
        emoji:
          type: string
          description: 图标
        label:
          type: string
          description: 文本
        style:
          type: integer
          format: int32
          description: '样式: 2（Primary）、3（Green）'
        type:
          type: integer
          format: int32
          description: 类型，系统内部使用
      title: 可执行按钮
      x-apifox-orders:
        - customId
        - emoji
        - label
        - style
        - type
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
  securitySchemes: {}
servers:
  - url: https://api.302.ai
    description: 正式环境
  - url: https://api.302ai.cn
    description: 国内中转
security: []

```

import requests

url = "https://api.302.ai/mj-turbo/task/1/fetch"

payload={}
headers = {
   'mj-api-secret': ''
}

response = requests.request("GET", url, headers=headers, data=payload)

print(response.text)

import http.client

conn = http.client.HTTPSConnection("api.302.ai")
payload = ''
headers = {
   'mj-api-secret': ''
}
conn.request("GET", "/mj-turbo/task/1/fetch", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))

