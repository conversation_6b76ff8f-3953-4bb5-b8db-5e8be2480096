#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
换装工作流服务
Fashion Workflow Service

实现全自动的工作流：用户输入 → AI整理 → 自动触发换装 → 结果展示
"""

import os
import json
import streamlit as st
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path

from services.ai_service import AIService
from services.team_service import TeamService
from services.player_service import PlayerService
from services.auth_service import AuthService


class FashionWorkflowService:
    """换装工作流服务 - 连接AI数据和换装功能"""
    
    def __init__(self, user_id: str = None):
        """初始化工作流服务"""
        self.auth_service = AuthService()
        self.user_id = user_id or self.auth_service.get_current_user_id()
        
        self.ai_service = AIService(self.user_id)
        self.team_service = TeamService()
        self.player_service = PlayerService()
        
        # 工作流状态存储路径
        self.workflow_data_path = os.path.join(
            self.auth_service.get_user_data_path(self.user_id),
            "fashion_workflow"
        )
        os.makedirs(self.workflow_data_path, exist_ok=True)
    
    def check_fashion_readiness(self, team_name: str) -> Dict[str, Any]:
        """
        检查球队是否准备好进行换装
        
        Args:
            team_name: 球队名称
            
        Returns:
            Dict: 准备状态信息
        """
        try:
            # 1. 检查AI导出数据
            ai_export_data = self._load_ai_export_data(team_name)
            if not ai_export_data:
                return {
                    "ready": False,
                    "reason": "no_ai_data",
                    "message": "未找到AI整理的球队数据"
                }
            
            # 2. 检查球员照片
            players_with_photos = []
            players_without_photos = []
            
            for player in ai_export_data.get("players", []):
                photo_info = player.get("photo_info", {})
                if photo_info.get("exists", False) and photo_info.get("absolute_path"):
                    players_with_photos.append(player)
                else:
                    players_without_photos.append(player)
            
            # 3. 评估准备状态
            total_players = len(ai_export_data.get("players", []))
            ready_players = len(players_with_photos)
            
            if ready_players == 0:
                return {
                    "ready": False,
                    "reason": "no_photos",
                    "message": f"球队有{total_players}名球员，但没有球员照片",
                    "total_players": total_players,
                    "ready_players": ready_players
                }
            
            # 4. 检查换装功能可用性
            if not self.ai_service.is_fashion_tryon_available():
                return {
                    "ready": False,
                    "reason": "fashion_unavailable",
                    "message": "换装功能不可用",
                    "total_players": total_players,
                    "ready_players": ready_players
                }
            
            return {
                "ready": True,
                "message": f"准备就绪！{ready_players}/{total_players}名球员有照片",
                "total_players": total_players,
                "ready_players": ready_players,
                "players_with_photos": players_with_photos,
                "players_without_photos": players_without_photos,
                "ai_export_data": ai_export_data
            }
            
        except Exception as e:
            return {
                "ready": False,
                "reason": "error",
                "message": f"检查准备状态时出错: {str(e)}"
            }
    
    def trigger_auto_fashion_workflow(self, team_name: str, clothes_image_path: str = None) -> Dict[str, Any]:
        """
        触发自动换装工作流
        
        Args:
            team_name: 球队名称
            clothes_image_path: 衣服图片路径（可选，如果不提供则使用默认）
            
        Returns:
            Dict: 工作流执行结果
        """
        try:
            # 1. 检查准备状态
            readiness = self.check_fashion_readiness(team_name)
            if not readiness["ready"]:
                return {
                    "success": False,
                    "error": readiness["message"],
                    "readiness": readiness
                }
            
            # 2. 准备换装数据
            players_with_photos = readiness["players_with_photos"]
            player_images = [
                player["photo_info"]["absolute_path"] 
                for player in players_with_photos
            ]
            
            # 3. 使用默认衣服图片（如果没有提供）
            if not clothes_image_path:
                clothes_image_path = self._get_default_clothes_image()

            if not clothes_image_path or not os.path.exists(clothes_image_path):
                return {
                    "success": False,
                    "error": "未找到衣服图片，请先选择模板或上传球队服装图片"
                }
            
            # 4. 自动生成队徽（在AI换装前）
            logo_path = self._auto_generate_team_logo(team_name)

            # 5. 执行批量换装
            st.info("🎨 开始自动换装流程...")

            fashion_result = self.ai_service.fashion_tryon_batch(
                player_images, clothes_image_path, team_name
            )

            # 6. 建立图片路径映射关系
            player_photo_mapping = self._create_player_photo_mapping(
                players_with_photos, fashion_result, team_name
            )

            # 7. 保存工作流结果
            workflow_result = {
                "team_name": team_name,
                "execution_time": datetime.now().isoformat(),
                "readiness_check": readiness,
                "fashion_result": fashion_result,
                "clothes_image_path": clothes_image_path,
                "processed_players": len(player_images),
                "player_photo_mapping": player_photo_mapping,
                "logo_path": logo_path  # 添加队徽路径
            }

            self._save_workflow_result(team_name, workflow_result)

            # 8. 自动触发Word生成
            if fashion_result.get("success", False) and fashion_result.get("successful_count", 0) > 0:
                st.info("📄 开始自动生成Word报名表...")
                word_result = self._auto_generate_word_document(team_name, player_photo_mapping, logo_path)
                workflow_result["word_generation_result"] = word_result

            return {
                "success": True,
                "workflow_result": workflow_result,
                "message": f"自动换装完成！处理了{len(player_images)}名球员"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"自动换装工作流执行失败: {str(e)}"
            }
    
    def _load_ai_export_data(self, team_name: str) -> Optional[Dict]:
        """加载AI导出的数据"""
        try:
            # 方法1：查找AI聊天组件保存的数据（新方式）
            user_id = st.session_state.get('user_id', 'default_user')
            ai_chat_data_path = os.path.join(
                'data', user_id, 'enhanced_ai_data', f'{team_name}_ai_data.json'
            )

            if os.path.exists(ai_chat_data_path):
                with open(ai_chat_data_path, 'r', encoding='utf-8') as f:
                    ai_chat_data = json.load(f)

                # 转换AI聊天数据格式为换装所需格式
                if 'extracted_info' in ai_chat_data:
                    return self._convert_ai_chat_data_to_export_format(ai_chat_data, team_name)

            # 方法2：查找传统AI导出文件（兼容旧方式）
            ai_export_path = os.path.join(
                os.path.dirname(os.path.dirname(__file__)),
                "ai_export",
                f"team_{team_name}_ai_ready.json"
            )

            if os.path.exists(ai_export_path):
                with open(ai_export_path, 'r', encoding='utf-8') as f:
                    return json.load(f)

            return None

        except Exception as e:
            st.error(f"加载AI导出数据失败: {e}")
            return None

    def _convert_ai_chat_data_to_export_format(self, ai_chat_data: Dict, team_name: str) -> Dict:
        """将AI聊天数据转换为换装所需的导出格式"""
        try:
            # 获取球员数据
            team_data = self.team_service.load_team_data_for_user(
                st.session_state.get('user_id', 'default_user'),
                team_name
            )

            players = []
            if team_data and 'players' in team_data:
                for player in team_data['players']:
                    # 构建球员照片信息
                    photo_info = {
                        "exists": bool(player.get('photo')),
                        "filename": player.get('photo', ''),
                        "absolute_path": ""
                    }

                    # 构建照片绝对路径
                    if player.get('photo'):
                        user_id = st.session_state.get('user_id', 'default_user')
                        photo_path = os.path.join('data', user_id, 'photos', player['photo'])
                        if os.path.exists(photo_path):
                            photo_info["absolute_path"] = os.path.abspath(photo_path)

                    players.append({
                        "id": player.get('id', ''),
                        "name": player.get('name', ''),
                        "jersey_number": player.get('jersey_number', ''),
                        "photo_info": photo_info
                    })

            # 构建导出格式
            export_data = {
                "team_info": {
                    "name": team_name,
                    "display_name": team_name,
                    "ai_extracted_info": ai_chat_data.get('extracted_info', {}),
                    "created_at": ai_chat_data.get('created_at', ''),
                    "updated_at": ai_chat_data.get('updated_at', '')
                },
                "players": players,
                "export_time": ai_chat_data.get('updated_at', ''),
                "data_source": "ai_chat_component"
            }

            return export_data

        except Exception as e:
            st.error(f"转换AI聊天数据格式失败: {e}")
            return None
    
    def _get_default_clothes_image(self) -> Optional[str]:
        """获取默认的衣服图片路径（集成新的模板系统）"""
        try:
            # 使用新的模板服务获取默认模板
            from services.template_service import TemplateService
            template_service = TemplateService(self.user_id)

            # 获取系统默认模板
            system_templates = template_service.get_system_templates()

            # 优先使用足球球衣模板
            if system_templates.get("football_jerseys"):
                return system_templates["football_jerseys"][0]["path"]

            # 其次使用其他类别的第一个模板
            for category, templates in system_templates.items():
                if templates:
                    return templates[0]["path"]

            # 如果没有系统模板，尝试旧的默认路径
            default_clothes_dir = os.path.join(
                os.path.dirname(os.path.dirname(__file__)),
                "assets",
                "default_clothes"
            )

            # 在各个子目录中查找图片
            for subdir in ["football_jerseys", "casual_wear", "formal_wear"]:
                subdir_path = os.path.join(default_clothes_dir, subdir)
                if os.path.exists(subdir_path):
                    for filename in os.listdir(subdir_path):
                        if filename.lower().endswith(('.jpg', '.jpeg', '.png')):
                            return os.path.join(subdir_path, filename)

            # 最后尝试根目录下的默认文件
            if os.path.exists(default_clothes_dir):
                for ext in ['.jpg', '.jpeg', '.png']:
                    default_path = os.path.join(default_clothes_dir, f"default_jersey{ext}")
                    if os.path.exists(default_path):
                        return default_path

            return None

        except Exception as e:
            st.warning(f"获取默认模板失败: {e}")
            return None
    
    def _save_workflow_result(self, team_name: str, result: Dict):
        """保存工作流结果"""
        try:
            result_file = os.path.join(
                self.workflow_data_path,
                f"workflow_{team_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            )
            
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            st.warning(f"保存工作流结果失败: {e}")
    
    def get_workflow_history(self, team_name: str) -> List[Dict]:
        """获取工作流历史记录"""
        try:
            history = []
            for file in os.listdir(self.workflow_data_path):
                if file.startswith(f"workflow_{team_name}_") and file.endswith('.json'):
                    file_path = os.path.join(self.workflow_data_path, file)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        history.append(json.load(f))
            
            # 按时间排序
            history.sort(key=lambda x: x.get("execution_time", ""), reverse=True)
            return history
            
        except Exception as e:
            st.error(f"获取工作流历史失败: {e}")
            return []

    def _create_player_photo_mapping(self, players_with_photos: List[Dict], fashion_result: Dict, team_name: str) -> Dict[str, str]:
        """
        创建球员与换装后图片的映射关系（基于用户文件夹系统）

        Args:
            players_with_photos: 有照片的球员列表
            fashion_result: 换装结果
            team_name: 球队名称

        Returns:
            Dict[str, str]: 球员ID到换装后图片路径的映射
        """
        mapping = {}

        try:
            if not fashion_result.get("success", False):
                st.warning("换装失败，无法创建图片映射")
                return mapping

            results = fashion_result.get("results", [])

            # 确保球员数量和结果数量匹配
            if len(players_with_photos) != len(results):
                st.warning(f"球员数量({len(players_with_photos)})与换装结果数量({len(results)})不匹配")

            # 获取用户专属的processed_photos文件夹
            user_processed_folder = self.auth_service.get_user_data_path(self.user_id, "processed_photos")
            team_processed_folder = os.path.join(user_processed_folder, team_name)
            os.makedirs(team_processed_folder, exist_ok=True)

            # 创建映射关系并移动图片到用户文件夹
            for i, player in enumerate(players_with_photos):
                if i < len(results):
                    result = results[i]
                    player_id = player.get("id", "")
                    player_name = player.get("name", "")

                    # 获取换装后的最终图片路径
                    if result.get("success", False) and result.get("final_result"):
                        temp_image_path = result["final_result"]

                        # 生成用户专属的图片路径
                        safe_player_name = "".join(c for c in player_name if c.isalnum() or c in (' ', '-', '_')).strip()
                        image_filename = f"{player_id}_{safe_player_name}_fashion_final.png"
                        user_image_path = os.path.join(team_processed_folder, image_filename)

                        # 移动图片到用户文件夹
                        if os.path.exists(temp_image_path):
                            import shutil
                            try:
                                shutil.copy2(temp_image_path, user_image_path)
                                mapping[player_id] = user_image_path
                                st.success(f"✅ 球员 {player_name} 图片已保存到: {image_filename}")

                                # 记录详细保存信息
                                st.info(f"📁 源文件: {temp_image_path}")
                                st.info(f"📁 目标路径: {user_image_path}")

                            except Exception as copy_error:
                                st.error(f"❌ 复制文件失败: {copy_error}")
                                st.error(f"📁 源文件: {temp_image_path}")
                                st.error(f"📁 目标路径: {user_image_path}")
                                # 使用原始照片路径
                                original_path = player.get("photo_info", {}).get("absolute_path", "")
                                if original_path:
                                    mapping[player_id] = original_path
                        else:
                            st.warning(f"⚠️ 球员 {player_name} 换装图片不存在: {temp_image_path}")
                            # 检查可能的文件位置
                            st.info(f"🔍 查找的文件路径: {temp_image_path}")
                            # 使用原始照片路径
                            original_path = player.get("photo_info", {}).get("absolute_path", "")
                            if original_path:
                                mapping[player_id] = original_path
                    else:
                        st.warning(f"⚠️ 球员 {player_name} 换装失败，使用原始照片")
                        # 如果换装失败，使用原始照片路径
                        original_path = player.get("photo_info", {}).get("absolute_path", "")
                        if original_path:
                            mapping[player_id] = original_path

            st.info(f"📋 成功创建 {len(mapping)} 个球员的图片映射关系")
            return mapping

        except Exception as e:
            st.error(f"创建图片映射失败: {e}")
            return mapping

    def _auto_generate_word_document(self, team_name: str, player_photo_mapping: Dict[str, str],
                                   logo_path: str = None) -> Dict[str, Any]:
        """
        自动生成Word文档（基于用户文件夹系统）

        Args:
            team_name: 球队名称
            player_photo_mapping: 球员ID到换装后图片路径的映射
            logo_path: 队徽图片路径（可选）

        Returns:
            Dict[str, Any]: Word生成结果
        """
        try:
            # 1. 获取球队数据
            team_data = self.team_service.load_team_data_for_user(self.user_id, team_name)

            if not team_data:
                return {
                    "success": False,
                    "error": f"未找到球队数据: {team_name}"
                }

            # 2. 添加队徽信息到球队数据
            if logo_path and os.path.exists(logo_path):
                team_data['logo_path'] = logo_path
                st.info(f"📋 已添加队徽到报名表: {os.path.basename(logo_path)}")
            else:
                st.info("📋 未提供队徽，将生成无队徽的报名表")

            # 3. 准备球员数据，使用换装后的图片路径
            players_data = []
            for player in team_data.get('players', []):
                player_id = player.get('id', '')
                player_data = {
                    'name': player.get('name', ''),
                    'jersey_number': player.get('jersey_number', ''),
                    'photo': player_photo_mapping.get(player_id, player.get('photo', ''))  # 使用换装后的图片
                }
                players_data.append(player_data)

            # 4. 创建用户专属的Word输出文件夹
            user_word_output = os.path.join(self.auth_service.get_user_data_path(self.user_id), "word_output")
            os.makedirs(user_word_output, exist_ok=True)

            # 5. 初始化Word生成服务（使用用户专属路径）
            from word_generator_service import WordGeneratorService
            from config.settings import app_settings

            # 获取模板路径（全局）和用户专属输出路径
            global_paths = app_settings.word_generator.get_absolute_paths()
            word_service = WordGeneratorService(
                jar_path=global_paths['jar_path'],
                template_path=global_paths['template_path'],
                output_dir=user_word_output  # 使用用户专属的输出目录
            )

            # 6. 生成Word文档
            st.info("📄 正在生成Word报名表...")
            result = word_service.generate_report(team_data, players_data)

            if result['success']:
                st.success(f"✅ Word报名表生成成功！")
                st.info(f"📁 文件保存在用户文件夹: {os.path.basename(result['file_path'])}")

                # 提供下载按钮
                if os.path.exists(result['file_path']):
                    with open(result['file_path'], 'rb') as f:
                        st.download_button(
                            label="📥 下载报名表",
                            data=f.read(),
                            file_name=os.path.basename(result['file_path']),
                            mime="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                            use_container_width=True
                        )

                # 记录Word文档路径到工作流结果中
                result['user_file_path'] = result['file_path']
                result['relative_path'] = os.path.relpath(result['file_path'], self.auth_service.get_user_data_path(self.user_id))
            else:
                st.error(f"❌ Word报名表生成失败: {result.get('message', '未知错误')}")

            return result

        except Exception as e:
            error_msg = f"自动生成Word文档失败: {str(e)}"
            st.error(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg
            }

    def _auto_generate_team_logo(self, team_name: str) -> Optional[str]:
        """
        自动生成球队队徽

        Args:
            team_name: 球队名称

        Returns:
            Optional[str]: 队徽文件路径，失败返回None
        """
        try:
            # 检查是否已有队徽
            from services.ai_image_generation_service import AIImageGenerationService
            image_service = AIImageGenerationService(self.user_id)

            existing_logo = image_service.get_team_logo_path(team_name)
            if existing_logo:
                st.info(f"✅ 使用现有队徽: {os.path.basename(existing_logo)}")
                return existing_logo

            # 自动生成队徽
            st.info(f"🎨 正在为 {team_name} 自动生成队徽...")

            # 使用增强AI服务生成队徽
            from services.enhanced_ai_service import enhanced_ai_assistant

            if enhanced_ai_assistant.is_available():
                # 调用队徽生成函数
                logo_result = enhanced_ai_assistant._generate_team_logo({
                    "team_name": team_name,
                    "team_style": "现代",
                    "color_preference": "蓝色"
                })

                if logo_result.get("success") and logo_result.get("logo_file_path"):
                    st.success(f"✅ 队徽自动生成成功!")
                    return logo_result["logo_file_path"]
                else:
                    st.warning(f"⚠️ 队徽生成失败: {logo_result.get('error', '未知错误')}")
            else:
                st.warning("⚠️ AI服务不可用，跳过队徽生成")

            return None

        except Exception as e:
            st.warning(f"⚠️ 自动生成队徽失败: {e}")
            return None
