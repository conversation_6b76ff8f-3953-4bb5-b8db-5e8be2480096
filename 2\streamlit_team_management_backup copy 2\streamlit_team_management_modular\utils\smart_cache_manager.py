#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能缓存管理器
Smart Cache Manager

提供多层级、智能的缓存策略，显著提升应用性能
"""

import streamlit as st
import hashlib
import json
import os
import time
from typing import Any, Dict, List, Optional, Callable, Union
from datetime import datetime, timedelta
from functools import wraps
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class CacheConfig:
    """缓存配置"""
    ttl: int = 300  # 生存时间（秒）
    max_entries: int = 100  # 最大条目数
    priority: str = "normal"  # 优先级：critical, important, normal
    user_isolated: bool = True  # 是否用户隔离
    version_sensitive: bool = True  # 是否版本敏感
    show_spinner: bool = False  # 是否显示加载动画


class SmartCacheManager:
    """智能缓存管理器"""
    
    def __init__(self):
        self.cache_stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0
        }
        self._initialize_cache_layers()
    
    def _initialize_cache_layers(self):
        """初始化缓存层级"""
        try:
            # L1: Session State缓存（会话级）
            if hasattr(st, 'session_state') and "smart_cache_l1" not in st.session_state:
                st.session_state.smart_cache_l1 = {}
        except Exception as e:
            logger.warning(f"L1缓存初始化失败: {e}")
            # 在没有Streamlit上下文时使用内存缓存
            if not hasattr(self, '_fallback_l1_cache'):
                self._fallback_l1_cache = {}
        
        # L2: Streamlit缓存（应用级）
        # 通过装饰器实现
        
        # L3: 文件系统缓存（持久化）
        self.fs_cache_dir = "cache"
        os.makedirs(self.fs_cache_dir, exist_ok=True)
    
    def generate_cache_key(self, func_name: str, args: tuple, kwargs: dict, 
                          config: CacheConfig) -> str:
        """生成缓存键"""
        key_parts = [func_name]
        
        # 添加用户隔离
        if config.user_isolated:
            user_id = st.session_state.get('user_id', 'anonymous')
            key_parts.append(f"user_{user_id}")
        
        # 添加参数哈希
        args_str = str(args) + str(sorted(kwargs.items()))
        args_hash = hashlib.md5(args_str.encode()).hexdigest()[:8]
        key_parts.append(args_hash)
        
        # 添加版本信息（如果需要）
        if config.version_sensitive:
            # 可以基于文件修改时间或数据版本
            version = self._get_data_version(args, kwargs)
            if version:
                key_parts.append(f"v_{version}")
        
        return "_".join(key_parts)
    
    def _get_data_version(self, args: tuple, kwargs: dict) -> Optional[str]:
        """获取数据版本（基于文件修改时间等）"""
        try:
            # 检查是否有文件路径参数
            for arg in args:
                if isinstance(arg, str) and os.path.exists(arg):
                    mtime = os.path.getmtime(arg)
                    return str(int(mtime))
            
            # 检查team_name参数，获取对应文件的修改时间
            team_name = kwargs.get('team_name')
            if team_name:
                user_id = st.session_state.get('user_id', '')
                team_file = f"data/user_{user_id}/teams/{team_name}/team.json"
                if os.path.exists(team_file):
                    mtime = os.path.getmtime(team_file)
                    return str(int(mtime))
        except Exception:
            pass
        return None
    
    def smart_cache(self, config: CacheConfig = None):
        """智能缓存装饰器"""
        if config is None:
            config = CacheConfig()
        
        def decorator(func: Callable):
            @wraps(func)
            def wrapper(*args, **kwargs):
                # 生成缓存键
                cache_key = self.generate_cache_key(
                    func.__name__, args, kwargs, config
                )
                
                # L1缓存检查（Session State）
                l1_result = self._check_l1_cache(cache_key, config)
                if l1_result is not None:
                    self.cache_stats["hits"] += 1
                    return l1_result
                
                # L2缓存检查（Streamlit Cache）
                l2_result = self._check_l2_cache(cache_key, func, args, kwargs, config)
                if l2_result is not None:
                    self.cache_stats["hits"] += 1
                    # 回写到L1缓存
                    self._set_l1_cache(cache_key, l2_result, config)
                    return l2_result
                
                # 缓存未命中，执行函数
                self.cache_stats["misses"] += 1
                
                if config.show_spinner:
                    with st.spinner(f"正在加载 {func.__name__}..."):
                        result = func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # 保存到各级缓存
                self._set_l1_cache(cache_key, result, config)
                self._set_l2_cache(cache_key, result, config)
                
                return result
            
            return wrapper
        return decorator
    
    def _check_l1_cache(self, cache_key: str, config: CacheConfig) -> Any:
        """检查L1缓存（Session State）"""
        try:
            # 确保缓存已初始化
            if not hasattr(st, 'session_state') or 'smart_cache_l1' not in st.session_state:
                return None

            cache_data = st.session_state.smart_cache_l1.get(cache_key)
            if cache_data:
                # 检查TTL
                if time.time() - cache_data["timestamp"] < config.ttl:
                    return cache_data["value"]
                else:
                    # 过期，删除
                    del st.session_state.smart_cache_l1[cache_key]
        except Exception as e:
            logger.warning(f"L1缓存检查失败: {e}")
        return None
    
    def _set_l1_cache(self, cache_key: str, value: Any, config: CacheConfig):
        """设置L1缓存"""
        try:
            # 确保缓存已初始化
            if not hasattr(st, 'session_state'):
                return
            if 'smart_cache_l1' not in st.session_state:
                st.session_state.smart_cache_l1 = {}

            # 检查缓存大小限制
            if len(st.session_state.smart_cache_l1) >= config.max_entries:
                self._evict_l1_cache(config)

            st.session_state.smart_cache_l1[cache_key] = {
                "value": value,
                "timestamp": time.time(),
                "priority": config.priority
            }
        except Exception as e:
            logger.warning(f"L1缓存设置失败: {e}")
    
    def _evict_l1_cache(self, config: CacheConfig):
        """L1缓存淘汰策略"""
        try:
            cache = st.session_state.smart_cache_l1
            
            # 按优先级和时间排序
            priority_order = {"critical": 3, "important": 2, "normal": 1}
            
            items = list(cache.items())
            items.sort(key=lambda x: (
                priority_order.get(x[1]["priority"], 0),
                x[1]["timestamp"]
            ))
            
            # 删除最老的低优先级条目
            to_remove = len(items) - config.max_entries + 10  # 多删除一些，避免频繁淘汰
            for i in range(min(to_remove, len(items))):
                key = items[i][0]
                if cache[key]["priority"] != "critical":  # 不删除关键数据
                    del cache[key]
                    self.cache_stats["evictions"] += 1
        except Exception as e:
            logger.warning(f"L1缓存淘汰失败: {e}")
    
    def _check_l2_cache(self, cache_key: str, func: Callable, args: tuple, 
                       kwargs: dict, config: CacheConfig) -> Any:
        """检查L2缓存（Streamlit Cache）"""
        try:
            # 使用Streamlit的缓存机制
            cached_func = st.cache_data(
                ttl=config.ttl,
                max_entries=config.max_entries,
                show_spinner=False
            )(func)
            
            # 注意：这里实际上会执行函数，但Streamlit会处理缓存
            # 这是一个简化的实现，实际中可能需要更复杂的逻辑
            return None  # 让它走到函数执行
        except Exception as e:
            logger.warning(f"L2缓存检查失败: {e}")
        return None
    
    def _set_l2_cache(self, cache_key: str, value: Any, config: CacheConfig):
        """设置L2缓存（由Streamlit自动处理）"""
        pass
    
    def clear_cache(self, pattern: str = None, user_only: bool = True):
        """清除缓存"""
        try:
            if user_only and hasattr(st, 'session_state'):
                user_id = st.session_state.get('user_id', '')
                pattern = f"*user_{user_id}*" if not pattern else f"*user_{user_id}*{pattern}*"

            # 清除L1缓存
            if hasattr(st, 'session_state') and 'smart_cache_l1' in st.session_state:
                if pattern:
                    keys_to_remove = [
                        key for key in st.session_state.smart_cache_l1.keys()
                        if pattern.replace('*', '') in key
                    ]
                    for key in keys_to_remove:
                        del st.session_state.smart_cache_l1[key]
                else:
                    st.session_state.smart_cache_l1.clear()
            
            # 清除Streamlit缓存
            st.cache_data.clear()
            
        except Exception as e:
            logger.warning(f"清除缓存失败: {e}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self.cache_stats["hits"] + self.cache_stats["misses"]
        hit_rate = (self.cache_stats["hits"] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            "hit_rate": f"{hit_rate:.1f}%",
            "total_hits": self.cache_stats["hits"],
            "total_misses": self.cache_stats["misses"],
            "total_evictions": self.cache_stats["evictions"],
            "l1_cache_size": len(st.session_state.get("smart_cache_l1", {})),
            "cache_keys": list(st.session_state.get("smart_cache_l1", {}).keys())
        }
    
    def warm_up_cache(self, warm_up_functions: List[Callable]):
        """缓存预热"""
        try:
            with st.spinner("正在预热缓存..."):
                for func in warm_up_functions:
                    try:
                        func()
                    except Exception as e:
                        logger.warning(f"缓存预热失败 {func.__name__}: {e}")
        except Exception as e:
            logger.warning(f"缓存预热过程失败: {e}")


# 缓存管理器已禁用以解决数据一致性问题
# 全局缓存管理器实例（已禁用）
smart_cache = None

# 便捷的缓存装饰器（已禁用，返回原函数）
def cache_critical(ttl: int = 1800):
    """关键数据缓存（已禁用）"""
    def decorator(func):
        return func  # 直接返回原函数，不使用缓存
    return decorator

def cache_important(ttl: int = 600):
    """重要数据缓存（已禁用）"""
    def decorator(func):
        return func  # 直接返回原函数，不使用缓存
    return decorator

def cache_normal(ttl: int = 300):
    """普通数据缓存（已禁用）"""
    def decorator(func):
        return func  # 直接返回原函数，不使用缓存
    return decorator

def cache_with_spinner(ttl: int = 300):
    """带加载动画的缓存（已禁用）"""
    def decorator(func):
        return func  # 直接返回原函数，不使用缓存
    return decorator
