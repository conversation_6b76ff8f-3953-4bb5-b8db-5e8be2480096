#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化提示词更新
Test Simplified Prompt Update

验证第二个文件夹中的简化提示词修改
"""

import os
import sys

# 添加项目路径
sys.path.append('streamlit_team_management_modular')

def test_enhanced_ai_service():
    """测试增强AI服务的修改"""
    print("🧪 测试增强AI服务修改...")
    
    try:
        from services.enhanced_ai_service import enhanced_ai_service
        
        if not enhanced_ai_service.is_available():
            print("❌ AI服务不可用，跳过测试")
            return False
        
        # 测试队徽生成方法
        test_args = {
            "team_name": "测试球队",
            "team_style": "现代",
            "color_preference": "红色和白色"
        }
        
        print(f"📝 测试参数: {test_args}")
        
        # 调用队徽生成方法
        result = enhanced_ai_service._generate_team_logo(test_args)
        
        if result.get("success"):
            print("✅ 队徽生成成功!")
            print(f"   描述长度: {len(result.get('logo_description', ''))}")
            print(f"   球队名称: {result.get('team_name')}")
            print(f"   风格: {result.get('style')}")
            return True
        else:
            print(f"❌ 队徽生成失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_auto_generation():
    """测试自动队徽生成功能"""
    print("\n🧪 测试自动队徽生成功能...")
    
    try:
        from services.enhanced_ai_service import enhanced_ai_service
        
        if not enhanced_ai_service.is_available():
            print("❌ AI服务不可用，跳过测试")
            return False
        
        # 模拟提取的球队信息
        extracted_info = {
            "球队名称": "火焰战士",
            "球衣颜色": "橙色和黑色"
        }
        
        print(f"📝 模拟提取信息: {extracted_info}")
        
        # 测试自动生成逻辑
        auto_result = enhanced_ai_service._auto_generate_logo_if_possible(extracted_info)
        
        if auto_result:
            print("✅ 自动队徽生成成功!")
            print(f"   球队名称: {auto_result.get('team_name')}")
            print(f"   触发方式: {auto_result.get('auto_trigger')}")
            print(f"   已生成: {auto_result.get('generated')}")
            return True
        else:
            print("❌ 没有自动生成队徽")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_enhanced_ai_assistant():
    """测试增强AI助手的修改"""
    print("\n🧪 测试增强AI助手修改...")
    
    try:
        from services.enhanced_ai_assistant import enhanced_ai_assistant
        
        # 模拟球队数据
        team_data = {
            "basic_info": {
                "team_name": "闪电足球队",
                "contact_person": "张三"
            },
            "kit_colors": {
                "jersey_color": "蓝色和白色"
            }
        }
        
        print(f"📝 模拟球队数据: {team_data}")
        
        # 测试保存时自动生成逻辑
        auto_result = enhanced_ai_assistant._auto_generate_logo_on_save(team_data)
        
        if auto_result:
            print("✅ 保存时自动队徽生成成功!")
            print(f"   球队名称: {auto_result.get('team_name')}")
            print(f"   触发方式: {auto_result.get('auto_trigger')}")
            return True
        else:
            print("❌ 保存时没有自动生成队徽")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def check_prompt_content():
    """检查提示词内容"""
    print("\n🧪 检查提示词内容...")
    
    try:
        # 检查 enhanced_ai_service.py 中的提示词
        with open("streamlit_team_management_modular/services/enhanced_ai_service.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查是否包含简化提示词
        if "请为足球队" in content and "4. 寓意说明" in content:
            print("✅ enhanced_ai_service.py 包含正确的简化提示词")
        else:
            print("❌ enhanced_ai_service.py 提示词不正确")
            return False
        
        # 检查是否使用 gpt-4o
        if 'model="gpt-4o"' in content:
            print("✅ enhanced_ai_service.py 使用 gpt-4o 模型")
        else:
            print("❌ enhanced_ai_service.py 没有使用 gpt-4o 模型")
            return False
        
        # 检查是否有自动生成逻辑
        if "_auto_generate_logo_if_possible" in content:
            print("✅ enhanced_ai_service.py 包含自动生成逻辑")
        else:
            print("❌ enhanced_ai_service.py 缺少自动生成逻辑")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查提示词内容失败: {e}")
        return False

def show_current_prompt():
    """显示当前使用的提示词"""
    print("\n📋 当前使用的简化提示词:")
    print("=" * 50)
    
    prompt = """
    请为足球队"{team_name}"设计一个队徽描述。

    要求：
    - 风格：{team_style}
    - 颜色偏好：{color_preference}
    - 适合足球队使用
    - 简洁明了，易于识别
    - 体现团队精神

    请提供详细的设计描述，包括：
    1. 主要图案元素
    2. 颜色搭配
    3. 整体布局
    4. 寓意说明
    """
    
    print(prompt)
    
    print("\n🔧 系统提示词:")
    print("你是一个专业的队徽设计师，擅长为足球队设计有意义的队徽。")
    
    print("\n⚙️ 参数设置:")
    print("- 模型: gpt-4o")
    print("- 温度: 0.8")

def main():
    """主测试函数"""
    print("🚀 开始测试第二个文件夹的简化提示词修改...")
    print("=" * 60)
    
    # 显示当前提示词
    show_current_prompt()
    
    # 运行测试
    tests = [
        ("提示词内容检查", check_prompt_content),
        ("增强AI服务测试", test_enhanced_ai_service),
        ("自动队徽生成测试", test_auto_generation),
        ("增强AI助手测试", test_enhanced_ai_assistant)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 第二个文件夹修改测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有测试通过！第二个文件夹的简化提示词修改成功！")
        print("\n💡 修改完成:")
        print("   ✅ enhanced_ai_service.py - 更新为简化提示词和gpt-4o")
        print("   ✅ enhanced_ai_assistant.py - 添加保存时自动生成队徽")
        print("   ✅ ai_chat.py - 添加自动队徽显示逻辑")
        print("   ✅ 自动队徽生成功能完整实现")
        
        print("\n🎯 功能特点:")
        print("   - 使用简化高效的提示词")
        print("   - 完全自动化的队徽生成")
        print("   - 智能触发机制")
        print("   - 用户友好的界面显示")
    else:
        print(f"\n⚠️ 还有 {total-passed} 个问题需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    main()
