#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量时尚换装工具
Batch Fashion Try-On Tool

使用方法:
python batch_fashion_tryon.py --input_folder path/to/photos --clothes_image path/to/clothes.png

或者直接运行脚本，按提示输入文件路径
"""

import requests
import time
import os
import argparse
import json
from pathlib import Path
from PIL import Image
from datetime import datetime
from config import *

def create_output_dirs():
    """创建输出目录"""
    for dir_name in OUTPUT_DIRS.values():
        os.makedirs(dir_name, exist_ok=True)

def get_image_files(folder_path):
    """获取文件夹中的所有图片文件"""
    image_files = []
    folder = Path(folder_path)
    
    if not folder.exists():
        print(f"❌ 文件夹不存在: {folder_path}")
        return []
    
    for ext in SUPPORTED_FORMATS:
        image_files.extend(folder.glob(f"*{ext}"))
        image_files.extend(folder.glob(f"*{ext.upper()}"))
    
    return [str(f) for f in image_files]

def process_single_photo(model_image, clothes_image, photo_name):
    """处理单张照片的完整流程"""
    print(f"\n" + "="*80)
    print(f"🎯 开始处理: {photo_name}")
    print("="*80)
    
    start_time = time.time()
    result = {
        "photo_name": photo_name,
        "model_image": model_image,
        "start_time": datetime.now().isoformat(),
        "steps": {},
        "success": False,
        "final_result": None,
        "processing_time": 0,
        "cost_ptc": 0
    }
    
    # 步骤1: 换装
    step1_result = step1_fashion_tryon(model_image, clothes_image, photo_name)
    result["steps"]["step1_fashion"] = {
        "success": step1_result is not None,
        "result_path": step1_result,
        "cost_ptc": API_COSTS["fashion_tryon"] if step1_result else 0
    }
    
    if not step1_result:
        print(f"❌ [{photo_name}] 步骤1失败，跳过")
        result["processing_time"] = time.time() - start_time
        return result
    
    # 步骤2: 移除背景
    step2_result = step2_remove_background(step1_result, photo_name)
    result["steps"]["step2_remove_bg"] = {
        "success": step2_result is not None,
        "result_path": step2_result,
        "cost_ptc": API_COSTS["remove_background"] if step2_result else 0
    }
    
    if not step2_result:
        print(f"❌ [{photo_name}] 步骤2失败，跳过")
        result["processing_time"] = time.time() - start_time
        return result
    
    # 步骤3: 添加白底背景
    step3_result = step3_add_white_background(step2_result, photo_name)
    result["steps"]["step3_white_bg"] = {
        "success": step3_result is not None,
        "result_path": step3_result,
        "cost_ptc": API_COSTS["white_background"]
    }
    
    if step3_result:
        result["success"] = True
        result["final_result"] = step3_result
        print(f"🎉 [{photo_name}] 完整流程成功！")
    else:
        print(f"❌ [{photo_name}] 步骤3失败")
    
    # 计算总成本和时间
    result["cost_ptc"] = sum(step["cost_ptc"] for step in result["steps"].values())
    result["processing_time"] = time.time() - start_time
    result["end_time"] = datetime.now().isoformat()
    
    return result

# 从single_fashion_tryon.py复制核心函数
def step1_fashion_tryon(model_image, clothes_image, photo_name):
    """步骤1: 302.AI-ComfyUI 换装"""
    print(f"\n🎯 [{photo_name}] 步骤1: 302.AI-ComfyUI 换装")
    
    if not os.path.exists(model_image):
        print(f"❌ 模特图片不存在: {model_image}")
        return None
    
    if not os.path.exists(clothes_image):
        print(f"❌ 服装图片不存在: {clothes_image}")
        return None
    
    print(f"📸 模特图片: {model_image}")
    print(f"👕 服装图片: {clothes_image}")
    
    url = f"{BASE_URL}/302/comfyui/clothes-changer/create-task"
    headers = {"Authorization": f"Bearer {API_KEY}"}
    
    try:
        with open(model_image, 'rb') as model_file, open(clothes_image, 'rb') as clothes_file:
            files = {
                'modelImageFile': (os.path.basename(model_image), model_file, 'image/jpeg'),
                'clothesImageFile': (os.path.basename(clothes_image), clothes_file, 'image/png')
            }
            
            data = FASHION_TRYON_CONFIG
            
            print("📤 发送换装任务请求...")
            response = requests.post(url, headers=headers, files=files, data=data,
                                   timeout=TIMEOUT_CONFIG["request_timeout"])
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None
    
    print(f"📊 响应状态码: {response.status_code}")
    
    if response.status_code in [200, 201]:
        try:
            result = response.json()
            if result.get('code') == 200 and 'data' in result:
                task_id = result['data']['taskId']
                print(f"✅ 换装任务创建成功！任务ID: {task_id}")
                return wait_for_fashion_task(task_id, photo_name)
            else:
                print(f"❌ 任务创建失败: {result}")
                return None
        except Exception as e:
            print(f"❌ 解析响应失败: {e}")
            return None
    else:
        print(f"❌ API请求失败: {response.status_code}")
        return None

def wait_for_fashion_task(task_id, photo_name):
    """等待换装任务完成"""
    print(f"⏳ [{photo_name}] 等待换装任务完成...")
    
    url = f"{BASE_URL}/302/comfyui/clothes-changer/check-task-status"
    headers = {"Authorization": f"Bearer {API_KEY}"}
    
    max_attempts = TIMEOUT_CONFIG["max_retry_attempts"]
    attempt = 0
    
    while attempt < max_attempts:
        attempt += 1
        print(f"🔄 第{attempt}次查询...")
        
        params = {"taskId": task_id}
        try:
            response = requests.get(url, headers=headers, params=params,
                                  timeout=TIMEOUT_CONFIG["request_timeout"])
        except Exception as e:
            print(f"❌ 网络请求失败: {e}")
            time.sleep(TIMEOUT_CONFIG["task_check_interval"])
            continue
        
        if response.status_code == 200:
            try:
                result = response.json()
                status = result.get('data', 'UNKNOWN')
                
                print(f"📈 任务状态: {status}")
                
                if status == 'SUCCESS' and 'output' in result:
                    output = result['output']
                    result_url = output.get('resultUrl', '')
                    
                    print(f"🎉 换装任务完成！")
                    print(f"🔗 结果图URL: {result_url}")
                    
                    return download_image(result_url, f"{photo_name}_step1_fashion.png")
                    
                elif status in ['RUNNING', 'QUEUED', 'SUBMITTING']:
                    print(f"⏳ 任务进行中... 等待{TIMEOUT_CONFIG['task_check_interval']}秒后再次查询...")
                    time.sleep(TIMEOUT_CONFIG["task_check_interval"])
                    
                else:
                    print(f"❌ 任务失败，状态: {status}")
                    return None
                    
            except Exception as e:
                print(f"❌ 解析响应失败: {e}")
                time.sleep(TIMEOUT_CONFIG["task_check_interval"])
                continue
        else:
            print(f"❌ 查询失败: {response.status_code}")
            time.sleep(TIMEOUT_CONFIG["task_check_interval"])
            continue
    
    print("⏰ 等待超时")
    return None

def step2_remove_background(image_path, photo_name):
    """步骤2: Clipdrop Remove-background 移除背景"""
    print(f"\n🎯 [{photo_name}] 步骤2: Clipdrop Remove-background 移除背景")
    
    if not image_path or not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return None
    
    print(f"📸 输入图片: {image_path}")
    
    url = f"{BASE_URL}/clipdrop/remove-background/v1"
    headers = {"x-api-key": API_KEY}
    
    try:
        with open(image_path, 'rb') as image_file:
            files = {
                'image_file': (os.path.basename(image_path), image_file, 'image/png')
            }
            
            print("📤 发送背景移除请求...")
            response = requests.post(url, headers=headers, files=files,
                                   timeout=TIMEOUT_CONFIG["request_timeout"])
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None
    
    print(f"📊 响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        output_path = os.path.join(OUTPUT_DIRS["temp"], f"{photo_name}_step2_no_background.png")
        
        with open(output_path, 'wb') as f:
            f.write(response.content)
        
        print(f"✅ 背景移除完成！已保存: {output_path}")
        return output_path
    else:
        print(f"❌ 背景移除失败: {response.status_code}")
        return None

def step3_add_white_background(subject_image_path, photo_name):
    """步骤3: 本地PIL添加白底背景"""
    print(f"\n🎯 [{photo_name}] 步骤3: 本地PIL添加白底背景")
    
    if not subject_image_path or not os.path.exists(subject_image_path):
        print(f"❌ 主体图片不存在: {subject_image_path}")
        return None
    
    try:
        # 打开主体图片（已移除背景的PNG）
        subject = Image.open(subject_image_path).convert("RGBA")
        width, height = subject.size
        print(f"📏 图片尺寸: {width}x{height}")
        
        # 创建白色背景
        background = Image.new('RGB', (width, height), WHITE_BACKGROUND_CONFIG["background_color"])
        
        # 将主体图片合成到白色背景上
        background.paste(subject, (0, 0), subject)
        
        # 保存结果
        output_path = os.path.join(OUTPUT_DIRS["batch_results"], f"{photo_name}_final_white_background.png")
        background.save(output_path, WHITE_BACKGROUND_CONFIG["output_format"],
                       dpi=(IMAGE_QUALITY["dpi"], IMAGE_QUALITY["dpi"]))
        
        print(f"✅ 白底背景合成完成！已保存: {output_path}")
        return output_path
        
    except Exception as e:
        print(f"❌ 图片处理错误: {e}")
        return None

def download_image(url, filename):
    """下载图片"""
    if not url:
        print("❌ URL为空，无法下载")
        return None
    
    try:
        print(f"📥 下载图片: {filename}")
        response = requests.get(url, timeout=TIMEOUT_CONFIG["request_timeout"])
        if response.status_code == 200:
            filepath = os.path.join(OUTPUT_DIRS["temp"], filename)
            
            with open(filepath, 'wb') as f:
                f.write(response.content)
            print(f"✅ 图片已保存: {filepath}")
            return filepath
        else:
            print(f"❌ 下载失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 下载错误: {e}")
        return None

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='批量时尚换装工具')
    parser.add_argument('--input_folder', type=str, help='输入图片文件夹路径')
    parser.add_argument('--clothes_image', type=str, help='服装图片路径')
    
    args = parser.parse_args()
    
    # 验证配置
    try:
        validate_config()
    except ValueError as e:
        print(f"❌ 配置错误: {e}")
        return
    
    # 创建输出目录
    create_output_dirs()
    
    # 获取输入路径
    if args.input_folder and args.clothes_image:
        input_folder = args.input_folder
        clothes_image = args.clothes_image
    else:
        print("请输入文件路径:")
        input_folder = input("图片文件夹路径: ").strip()
        clothes_image = input("服装图片路径: ").strip()
    
    # 获取所有图片文件
    image_files = get_image_files(input_folder)
    if not image_files:
        print(f"❌ 在文件夹 {input_folder} 中未找到支持的图片文件")
        return
    
    print("=" * 100)
    print("🎯 批量时尚换装工具")
    print("=" * 100)
    print("📝 工作流程:")
    print("   1. 302.AI-ComfyUI 换装")
    print("   2. Clipdrop Remove-background 移除背景")
    print("   3. 本地PIL 添加白底背景")
    print()
    print(f"📊 找到图片: {len(image_files)} 张")
    print(f"👕 服装模板: {clothes_image}")
    print(f"💰 预估总成本: {len(image_files) * get_total_cost_per_image()} PTC (约{len(image_files) * get_total_cost_cny_per_image():.1f}元)")
    print()
    
    batch_start_time = time.time()
    batch_results = {
        "start_time": datetime.now().isoformat(),
        "total_photos": len(image_files),
        "results": [],
        "summary": {}
    }
    
    # 处理每张照片
    for i, photo_path in enumerate(image_files, 1):
        photo_name = Path(photo_path).stem
        print(f"\n🔄 进度: {i}/{len(image_files)} - {photo_name}")
        
        result = process_single_photo(photo_path, clothes_image, photo_name)
        batch_results["results"].append(result)
        
        # 显示当前进度
        success_count = sum(1 for r in batch_results["results"] if r["success"])
        print(f"📈 当前成功率: {success_count}/{i} ({success_count/i*100:.1f}%)")
    
    # 生成批量测试总结
    batch_end_time = time.time()
    batch_results["end_time"] = datetime.now().isoformat()
    batch_results["total_time"] = batch_end_time - batch_start_time
    
    # 计算统计数据
    successful_results = [r for r in batch_results["results"] if r["success"]]
    total_cost = sum(r["cost_ptc"] for r in batch_results["results"])
    avg_time = sum(r["processing_time"] for r in batch_results["results"]) / len(batch_results["results"])
    
    batch_results["summary"] = {
        "total_photos": len(image_files),
        "successful_photos": len(successful_results),
        "success_rate": len(successful_results) / len(image_files) * 100,
        "total_cost_ptc": total_cost,
        "total_cost_cny": total_cost * PTC_TO_CNY,
        "average_time_per_photo": avg_time,
        "total_processing_time": batch_results["total_time"]
    }
    
    # 保存批量测试报告
    report_path = os.path.join(OUTPUT_DIRS["analysis"], "batch_test_report.json")
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(batch_results, f, ensure_ascii=False, indent=2)
    
    # 显示最终结果
    print("\n" + "=" * 100)
    print("🎉 批量测试完成！")
    print("=" * 100)
    print(f"📊 总体统计:")
    print(f"   📸 测试照片: {batch_results['summary']['total_photos']} 张")
    print(f"   ✅ 成功处理: {batch_results['summary']['successful_photos']} 张")
    print(f"   🏆 成功率: {batch_results['summary']['success_rate']:.1f}%")
    print(f"   💰 总成本: {batch_results['summary']['total_cost_ptc']:.1f} PTC (约{batch_results['summary']['total_cost_cny']:.1f}元)")
    print(f"   ⏱️  平均处理时间: {batch_results['summary']['average_time_per_photo']:.1f}秒/张")
    print(f"   🕐 总处理时间: {batch_results['summary']['total_processing_time']:.1f}秒")
    print()
    print(f"📁 批量测试报告: {report_path}")
    print(f"📁 结果文件夹: {OUTPUT_DIRS['batch_results']}/")
    print("=" * 100)

if __name__ == "__main__":
    main()
