#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件上传调试页面
用于深度诊断文件上传组件问题
"""

import streamlit as st
import time
import sys
import os
import platform
import traceback
from datetime import datetime

def main():
    st.set_page_config(
        page_title="文件上传调试器",
        page_icon="🔧",
        layout="wide"
    )
    
    st.title("🔧 文件上传深度调试器")
    st.markdown("---")
    
    # 系统信息
    st.header("🖥️ 系统信息")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.subheader("Python环境")
        st.write(f"Python版本: {sys.version}")
        st.write(f"Streamlit版本: {st.__version__}")
        st.write(f"操作系统: {platform.system()} {platform.release()}")
    
    with col2:
        st.subheader("Session State")
        st.write(f"Keys数量: {len(st.session_state.keys())}")
        st.write(f"Keys: {list(st.session_state.keys())}")
        
        # 显示所有session state内容
        if st.checkbox("显示详细Session State"):
            for key, value in st.session_state.items():
                st.write(f"{key}: {type(value)} = {str(value)[:100]}")
    
    with col3:
        st.subheader("时间信息")
        st.write(f"当前时间: {datetime.now()}")
        st.write(f"时间戳: {int(time.time())}")
    
    st.markdown("---")
    
    # JavaScript调试
    st.header("🌐 JavaScript调试")
    
    # 添加JavaScript调试代码
    js_debug = """
    <script>
    console.log("=== Streamlit文件上传调试 ===");
    console.log("User Agent:", navigator.userAgent);
    console.log("Document Ready State:", document.readyState);

    // 检查文件API支持
    if (window.File && window.FileReader && window.FileList && window.Blob) {
        console.log("✅ 文件API完全支持");
    } else {
        console.log("❌ 文件API不支持");
    }

    // 监听文件上传相关事件
    document.addEventListener('click', function(e) {
        if (e.target.textContent && e.target.textContent.includes('Browse files')) {
            console.log("🔍 检测到Browse files按钮点击:", e.target);
            console.log("按钮类型:", e.target.tagName);
            console.log("按钮类名:", e.target.className);
            console.log("父元素:", e.target.parentElement);

            // 尝试检测文件对话框是否打开
            setTimeout(function() {
                console.log("🔍 检查文件对话框状态...");
                const fileInputs = document.querySelectorAll('input[type="file"]');
                fileInputs.forEach((input, index) => {
                    console.log(`文件输入 ${index}:`, input);
                    console.log(`  - 值:`, input.value);
                    console.log(`  - 文件:`, input.files);
                });
            }, 1000);
        }
    });

    // 监听文件输入变化
    document.addEventListener('change', function(e) {
        if (e.target.type === 'file') {
            console.log("📁 文件输入变化:", e.target);
            console.log("选择的文件:", e.target.files);

            // 在页面上显示文件选择状态
            const statusDiv = document.getElementById('file-status');
            if (statusDiv) {
                statusDiv.innerHTML = `✅ 文件已选择: ${e.target.files.length} 个文件`;
                statusDiv.style.color = 'green';
            }
        }
    });

    // 监听焦点事件（文件对话框可能会导致焦点变化）
    window.addEventListener('focus', function() {
        console.log("🔍 窗口获得焦点 - 可能是文件对话框关闭");
    });

    window.addEventListener('blur', function() {
        console.log("🔍 窗口失去焦点 - 可能是文件对话框打开");
    });

    // 检查是否有错误
    window.addEventListener('error', function(e) {
        console.error("❌ JavaScript错误:", e.error);
    });

    setTimeout(function() {
        // 查找所有文件上传相关元素
        const fileInputs = document.querySelectorAll('input[type="file"]');
        console.log("找到的文件输入元素:", fileInputs.length);

        const browseButtons = document.querySelectorAll('button');
        const browseBtns = Array.from(browseButtons).filter(btn =>
            btn.textContent && btn.textContent.includes('Browse files')
        );
        console.log("找到的Browse files按钮:", browseBtns.length);

        browseBtns.forEach((btn, index) => {
            console.log(`Browse按钮 ${index}:`, btn);
            console.log(`  - 是否可见:`, btn.offsetParent !== null);
            console.log(`  - 是否禁用:`, btn.disabled);
            console.log(`  - 样式:`, window.getComputedStyle(btn).display);
        });
    }, 2000);
    </script>

    <div id="file-status" style="padding: 10px; margin: 10px 0; border: 1px solid #ccc; border-radius: 5px;">
        📁 文件选择状态：等待用户操作...
    </div>
    """
    
    st.markdown(js_debug, unsafe_allow_html=True)
    
    st.info("📝 请打开浏览器开发者工具(F12)查看Console输出")
    
    st.markdown("---")
    
    # 文件上传测试区域
    st.header("📁 文件上传测试区域")
    
    # 测试1: 最基础的上传
    st.subheader("测试1: 最基础上传")

    # 显示详细的调试信息
    st.write("🔍 **后端调试信息:**")
    st.write(f"- Streamlit版本: {st.__version__}")
    st.write(f"- Session State Keys: {list(st.session_state.keys())}")
    st.write(f"- 当前时间戳: {int(time.time())}")

    try:
        st.write("📝 准备创建file_uploader组件...")
        uploaded_file1 = st.file_uploader("基础测试", key="debug_basic")
        st.write("✅ file_uploader组件创建成功")

        if uploaded_file1:
            st.success(f"✅ 基础上传成功: {uploaded_file1.name}")
            st.write(f"文件大小: {uploaded_file1.size} bytes")
            st.write(f"文件类型: {uploaded_file1.type}")
        else:
            st.info("等待文件上传...")

    except Exception as e:
        st.error(f"❌ 基础上传错误: {e}")
        st.code(traceback.format_exc())
    
    # 测试2: 带类型限制
    st.subheader("测试2: 带类型限制")
    try:
        uploaded_file2 = st.file_uploader(
            "类型限制测试", 
            type=['png', 'jpg', 'jpeg'],
            key="debug_type"
        )
        if uploaded_file2:
            st.success(f"✅ 类型限制上传成功: {uploaded_file2.name}")
    except Exception as e:
        st.error(f"❌ 类型限制上传错误: {e}")
        st.code(traceback.format_exc())
    
    # 测试3: 多文件上传
    st.subheader("测试3: 多文件上传")
    try:
        uploaded_files3 = st.file_uploader(
            "多文件测试",
            accept_multiple_files=True,
            key="debug_multi"
        )
        if uploaded_files3:
            st.success(f"✅ 多文件上传成功: {len(uploaded_files3)} 个文件")
            for file in uploaded_files3:
                st.write(f"- {file.name}")
    except Exception as e:
        st.error(f"❌ 多文件上传错误: {e}")
        st.code(traceback.format_exc())
    
    # 测试4: 动态key
    st.subheader("测试4: 动态key测试")
    if 'debug_key' not in st.session_state:
        st.session_state.debug_key = str(int(time.time()))
    
    try:
        uploaded_file4 = st.file_uploader(
            "动态key测试",
            key=f"debug_dynamic_{st.session_state.debug_key}"
        )
        if uploaded_file4:
            st.success(f"✅ 动态key上传成功: {uploaded_file4.name}")
    except Exception as e:
        st.error(f"❌ 动态key上传错误: {e}")
        st.code(traceback.format_exc())
    
    st.markdown("---")
    
    # 浏览器兼容性测试
    st.header("🌍 浏览器兼容性测试")
    
    browser_test = """
    <div id="browser-test">
        <h4>浏览器特性检测:</h4>
        <div id="test-results"></div>
    </div>
    
    <script>
    function runBrowserTests() {
        const results = document.getElementById('test-results');
        let html = '';
        
        // 测试文件API
        html += '<p>📁 File API: ' + (window.File ? '✅ 支持' : '❌ 不支持') + '</p>';
        html += '<p>📖 FileReader: ' + (window.FileReader ? '✅ 支持' : '❌ 不支持') + '</p>';
        html += '<p>📋 FileList: ' + (window.FileList ? '✅ 支持' : '❌ 不支持') + '</p>';
        html += '<p>🔵 Blob: ' + (window.Blob ? '✅ 支持' : '❌ 不支持') + '</p>';
        
        // 测试拖拽API
        html += '<p>🖱️ Drag & Drop: ' + ('draggable' in document.createElement('div') ? '✅ 支持' : '❌ 不支持') + '</p>';
        
        // 测试FormData
        html += '<p>📝 FormData: ' + (window.FormData ? '✅ 支持' : '❌ 不支持') + '</p>';
        
        // 浏览器信息
        html += '<p>🌐 User Agent: ' + navigator.userAgent + '</p>';
        
        results.innerHTML = html;
    }
    
    // 页面加载完成后运行测试
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', runBrowserTests);
    } else {
        runBrowserTests();
    }
    </script>
    """
    
    st.markdown(browser_test, unsafe_allow_html=True)
    
    st.markdown("---")
    
    # 操作按钮
    st.header("🔧 调试操作")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🔄 重置Session State"):
            for key in list(st.session_state.keys()):
                if key.startswith('debug_'):
                    del st.session_state[key]
            st.rerun()
    
    with col2:
        if st.button("📊 生成调试报告"):
            st.json({
                "timestamp": datetime.now().isoformat(),
                "streamlit_version": st.__version__,
                "python_version": sys.version,
                "platform": platform.platform(),
                "session_keys": list(st.session_state.keys()),
                "session_count": len(st.session_state.keys())
            })
    
    with col3:
        if st.button("🧹 清理缓存"):
            st.cache_data.clear()
            st.cache_resource.clear()
            st.success("缓存已清理")

if __name__ == "__main__":
    main()
