#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
球员表单组件
Player Form Component

提供球员添加和编辑表单
"""

import streamlit as st
from typing import Optional, Tuple
import logging
import traceback
import time

from services.player_service import PlayerService
from config.constants import UIConstants, FileTypes

# 配置调试日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


class PlayerFormComponent:
    """球员表单组件"""
    
    def __init__(self):
        self.player_service = PlayerService()
    
    def render_add_form(self, team_name: str) -> bool:
        """
        渲染添加球员表单
        
        Args:
            team_name: 球队名称
            
        Returns:
            bool: 是否成功添加球员
        """
        # 初始化表单key，确保文件上传组件的唯一性
        if 'form_key' not in st.session_state:
            import time
            st.session_state.form_key = str(int(time.time()))

        st.subheader("添加新球员")

        with st.form("add_player_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                player_name = st.text_input("球员姓名 *", placeholder="请输入球员姓名")
                
                # 获取已使用的号码
                used_numbers = self.player_service.get_used_jersey_numbers(team_name)
                help_text = UIConstants.HELP_TEXTS["jersey_number"].format(
                    used_numbers=', '.join(used_numbers) if used_numbers else '无'
                )
                
                jersey_number = st.number_input(
                    "球衣号码 *", 
                    min_value=1, 
                    max_value=99, 
                    value=1,
                    help=help_text
                )
            
            with col2:
                # 球员照片上传
                st.markdown("**球员照片**")
                uploaded_photo = st.file_uploader(
                    "选择照片文件",
                    type=['png', 'jpg', 'jpeg', 'gif', 'bmp'],
                    key=f"photo_upload_{st.session_state.get('form_key', 'default')}"
                )

                # 添加详细的使用说明
                if uploaded_photo is None:
                    st.markdown(
                        """
                        <div style="background-color: #e8f4fd; padding: 12px; border-radius: 8px; border-left: 4px solid #1f77b4;">
                            <h4 style="margin: 0 0 8px 0; color: #1f77b4;">📸 如何上传照片：</h4>
                            <ul style="margin: 0; padding-left: 20px;">
                                <li><strong>方法1：</strong> 点击上方的"Browse files"按钮</li>
                                <li><strong>方法2：</strong> 直接拖拽图片文件到上传区域</li>
                                <li><strong>支持格式：</strong> PNG, JPG, JPEG, GIF, BMP</li>
                                <li><strong>建议大小：</strong> 小于10MB</li>
                            </ul>
                        </div>
                        """,
                        unsafe_allow_html=True
                    )

                    # 添加故障排除提示
                    with st.expander("🔧 上传按钮无反应？点击查看解决方案"):
                        st.markdown(
                            """
                            **可能的解决方案：**

                            1. **刷新页面**：按 F5 或 Ctrl+R 刷新浏览器
                            2. **清除缓存**：按 Ctrl+Shift+R 强制刷新
                            3. **换个浏览器**：尝试使用 Chrome、Firefox 或 Edge
                            4. **检查文件大小**：确保图片文件小于10MB
                            5. **检查文件格式**：确保是 PNG、JPG 等支持的格式
                            6. **禁用广告拦截器**：某些插件可能阻止文件上传

                            **如果问题仍然存在，请尝试：**
                            - 重启浏览器
                            - 使用无痕模式
                            - 联系技术支持
                            """
                        )
                
                # 显示照片预览
                if uploaded_photo is not None:
                    st.image(uploaded_photo, caption="照片预览", width=200)
            
            # 表单按钮
            col1, col2 = st.columns(2)
            with col1:
                submitted = st.form_submit_button("保存球员", type="primary")
            with col2:
                cancelled = st.form_submit_button("取消")
            
            if submitted:
                if player_name.strip() and jersey_number:
                    with st.spinner('🏗️ 正在添加球员...'):
                        success, message = self.player_service.add_player(
                            team_name, player_name.strip(), str(jersey_number), uploaded_photo
                        )

                    if success:
                        # 显示成功消息
                        st.success(f"🎉 {message}")

                        # 显示额外提示
                        st.info("✅ 球员添加成功！正在刷新球队信息...")

                        # 等待一小段时间确保数据完全保存
                        import time
                        time.sleep(0.5)

                        # 简单清除session state缓存
                        self._simple_cache_clear()

                        # 关闭表单并刷新
                        st.session_state.show_add_form = False
                        st.rerun()
                        return True
                    else:
                        st.error(f"❌ {message}")
                        if "号码" in message:
                            st.info("💡 提示：请选择其他号码，或检查现有球员列表")
                        elif "照片" in message:
                            st.info("💡 提示：请检查照片格式和大小")
                        else:
                            st.info("💡 提示：请检查输入信息是否正确")
                else:
                    st.error("❌ 请填写完整的球员信息")
                    st.info("💡 球员姓名和号码都是必填项")
            
            if cancelled:
                st.session_state.show_add_form = False
                st.rerun()
        
        return False
    
    def render_edit_form(self, team_name: str, player_id: str) -> bool:
        """
        渲染编辑球员表单
        
        Args:
            team_name: 球队名称
            player_id: 球员ID
            
        Returns:
            bool: 是否成功更新球员
        """
        player = self.player_service.get_player(team_name, player_id)
        if not player:
            st.error("球员不存在")
            return False
        
        st.subheader(f"编辑球员: {player.name}")
        
        with st.form("edit_player_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                player_name = st.text_input(
                    "球员姓名 *", 
                    value=player.name,
                    placeholder="请输入球员姓名"
                )
                
                # 获取已使用的号码（排除当前球员）
                used_numbers = self.player_service.get_used_jersey_numbers(team_name)
                if player.jersey_number in used_numbers:
                    used_numbers.remove(player.jersey_number)
                
                help_text = UIConstants.HELP_TEXTS["jersey_number"].format(
                    used_numbers=', '.join(used_numbers) if used_numbers else '无'
                )
                
                jersey_number = st.number_input(
                    "球衣号码 *",
                    min_value=1,
                    max_value=99,
                    value=int(player.jersey_number),
                    help=help_text
                )
            
            with col2:
                # 显示当前照片
                if player.photo:
                    photo_path = self.player_service.file_manager.get_photo_path(
                        team_name, player.photo
                    )
                    if photo_path:
                        st.image(photo_path, caption="当前照片", width=200)
                    else:
                        st.info("当前照片文件不存在")
                else:
                    st.info("暂无照片")
                
                # 上传新照片
                st.markdown("**更换照片**")
                new_photo = st.file_uploader(
                    "选择新照片文件",
                    type=['png', 'jpg', 'jpeg', 'gif', 'bmp'],
                    key=f"edit_photo_upload_{player_to_edit.name}_{st.session_state.get('edit_key', 'default')}"
                )

                if new_photo is None:
                    st.info("💡 选择新照片文件来替换当前照片")
                    st.markdown(
                        """
                        <div style="background-color: #fff3cd; padding: 8px; border-radius: 5px; border-left: 3px solid #ffc107;">
                            <small><strong>提示：</strong> 如果"Browse files"按钮无反应，请尝试刷新页面或使用拖拽方式上传</small>
                        </div>
                        """,
                        unsafe_allow_html=True
                    )
                
                if new_photo is not None:
                    st.image(new_photo, caption="新照片预览", width=200)
            
            # 表单按钮
            col1, col2 = st.columns(2)
            with col1:
                submitted = st.form_submit_button("保存修改", type="primary")
            with col2:
                cancelled = st.form_submit_button("取消")
            
            if submitted:
                # 更新基本信息
                player.update_info(player_name, str(jersey_number))
                
                # 更新照片（如果有新照片）
                if new_photo is not None:
                    success, message = self.player_service.update_player_photo(
                        team_name, player_id, new_photo
                    )
                    if not success:
                        st.error(message)
                        return False
                
                # 保存球员信息
                if self.player_service.player_repo.update_player(team_name, player):
                    st.success("球员信息更新成功")
                    st.session_state.show_edit_form = False
                    st.rerun()
                    return True
                else:
                    st.error("保存失败")
            
            if cancelled:
                st.session_state.show_edit_form = False
                st.rerun()
        
        return False
    
    def render_quick_add_buttons(self, team_name: str) -> None:
        """
        渲染快速添加按钮
        
        Args:
            team_name: 球队名称
        """
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("➕ 添加球员", use_container_width=True):
                st.session_state.show_add_form = True
                st.session_state.batch_mode = 'normal'
                st.rerun()
        
        with col2:
            if st.button("📤 批量添加", use_container_width=True):
                st.session_state.batch_mode = 'upload'
                st.session_state.batch_photos = []
                st.session_state.show_add_form = False
                st.rerun()

    def _simple_cache_clear(self):
        """简单清除session state缓存"""
        try:
            # 获取当前球队名称
            current_team = st.session_state.get('current_team', '')

            # 只清理session state中的相关缓存
            cache_keys_to_clear = [
                f'players_cache_{current_team}',
                f'team_stats_cache_{current_team}',
                'players_cache',
                'team_stats_cache',
                'player_list_cache',
                'last_team_stats'
            ]

            for key in cache_keys_to_clear:
                if key in st.session_state:
                    del st.session_state[key]

        except Exception as e:
            # 缓存清除失败不应该影响主要功能
            pass
